{"name": "forge-ec-website", "version": "1.0.0", "description": "Professional website for the Forge EC cryptography library", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "serve": "vite preview --port 3000", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage", "lint": "eslint . --ext .js,.ts,.vue", "lint:fix": "eslint . --ext .js,.ts,.vue --fix", "format": "prettier --write .", "build:analyze": "vite build --mode analyze", "optimize:images": "node scripts/optimize-images.js", "deploy": "npm run build && npm run deploy:gh-pages", "deploy:gh-pages": "gh-pages -d dist"}, "dependencies": {"@studio-freight/lenis": "^1.0.42", "@theatre/core": "^0.5.1", "@theatre/studio": "^0.5.1", "popmotion": "^11.0.5", "focus-trap": "^7.5.4", "quicklink": "^2.3.0"}, "devDependencies": {"vite": "^5.0.10", "vite-plugin-pwa": "^0.17.4", "workbox-build": "^7.0.0", "workbox-window": "^7.0.0", "sharp": "^0.33.1", "imagemin": "^8.0.1", "imagemin-webp": "^8.0.0", "imagemin-avif": "^0.1.5", "@vitejs/plugin-legacy": "^5.2.0", "terser": "^5.26.0", "rollup-plugin-visualizer": "^5.12.0", "eslint": "^8.56.0", "prettier": "^3.1.1", "vitest": "^1.1.3", "@vitest/ui": "^1.1.3", "@vitest/coverage-v8": "^1.1.3", "jsdom": "^23.2.0", "gh-pages": "^6.1.1", "@sentry/browser": "^7.93.0", "@axe-core/playwright": "^4.8.4", "lighthouse": "^11.4.0", "web-vitals": "^3.5.1"}, "keywords": ["cryptography", "elliptic-curves", "rust", "security", "forge-ec", "website", "documentation"], "author": "<PERSON><PERSON><PERSON>", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/tanmaypatil/forge-ec.git"}, "homepage": "https://tanmaypatil.github.io/forge-ec/", "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "browserslist": ["> 1%", "last 2 versions", "not dead", "not ie 11"]}
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Loading Screen Fix Test - Forge EC</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
        }
        
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            padding: 30px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
        }
        
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .test-button {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
            transition: background 0.3s;
        }
        
        .test-button:hover {
            background: #45a049;
        }
        
        .test-button.danger {
            background: #f44336;
        }
        
        .test-button.danger:hover {
            background: #da190b;
        }
        
        .status {
            margin-top: 15px;
            padding: 10px;
            border-radius: 5px;
            font-weight: bold;
        }
        
        .status.success {
            background: rgba(76, 175, 80, 0.3);
            border: 1px solid #4CAF50;
        }
        
        .status.error {
            background: rgba(244, 67, 54, 0.3);
            border: 1px solid #f44336;
        }
        
        .status.warning {
            background: rgba(255, 193, 7, 0.3);
            border: 1px solid #FFC107;
        }
        
        .log {
            background: rgba(0, 0, 0, 0.3);
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
            margin-top: 15px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🔧 Loading Screen Fix Test</h1>
        <p>This page tests the fixes implemented for the loading screen freezing issue on the Forge EC documentation website.</p>
        
        <div class="test-section">
            <h2>🧪 Browser Extension Error Simulation</h2>
            <p>Test how the page handles browser extension errors that cause "message port closed" errors.</p>
            <button class="test-button danger" onclick="simulateExtensionError()">Simulate Extension Error</button>
            <button class="test-button" onclick="testErrorSuppression()">Test Error Suppression</button>
            <div id="extension-status" class="status" style="display: none;"></div>
        </div>
        
        <div class="test-section">
            <h2>🔥 Firebase Timeout Simulation</h2>
            <p>Test how the page handles Firebase initialization failures and timeouts.</p>
            <button class="test-button danger" onclick="simulateFirebaseTimeout()">Simulate Firebase Timeout</button>
            <button class="test-button danger" onclick="simulateFirebaseError()">Simulate Firebase Error</button>
            <div id="firebase-status" class="status" style="display: none;"></div>
        </div>
        
        <div class="test-section">
            <h2>⏱️ Loading Screen Timeout Test</h2>
            <p>Test the multiple layers of loading screen protection.</p>
            <button class="test-button" onclick="testLoadingScreenTimeout()">Test Timeout Layers</button>
            <button class="test-button" onclick="createMockLoadingScreen()">Create Mock Loading Screen</button>
            <div id="timeout-status" class="status" style="display: none;"></div>
        </div>
        
        <div class="test-section">
            <h2>📊 Test Results</h2>
            <button class="test-button" onclick="runAllTests()">Run All Tests</button>
            <button class="test-button" onclick="clearLog()">Clear Log</button>
            <div id="test-log" class="log"></div>
        </div>
        
        <div class="test-section">
            <h2>🔗 Navigation</h2>
            <p>Test the actual documentation page with the fixes applied:</p>
            <a href="docs/getting-started/quick-start.html" class="test-button" style="text-decoration: none; display: inline-block;">
                Go to Quick Start Guide
            </a>
        </div>
    </div>

    <script>
        let testLog = [];
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            testLog.push(`[${timestamp}] ${type.toUpperCase()}: ${message}`);
            updateLogDisplay();
        }
        
        function updateLogDisplay() {
            const logElement = document.getElementById('test-log');
            logElement.innerHTML = testLog.join('\n');
            logElement.scrollTop = logElement.scrollHeight;
        }
        
        function clearLog() {
            testLog = [];
            updateLogDisplay();
        }
        
        function updateStatus(elementId, message, type) {
            const element = document.getElementById(elementId);
            element.textContent = message;
            element.className = `status ${type}`;
            element.style.display = 'block';
        }
        
        function simulateExtensionError() {
            log('Simulating browser extension error...', 'test');
            
            // Simulate the exact error that causes issues
            const error = new Error('Unchecked runtime.lastError: The message port closed before a response was received');
            
            // Trigger the error
            window.dispatchEvent(new ErrorEvent('error', {
                message: error.message,
                error: error
            }));
            
            updateStatus('extension-status', 'Extension error simulated - check console', 'warning');
            log('Extension error dispatched', 'success');
        }
        
        function testErrorSuppression() {
            log('Testing error suppression...', 'test');
            
            // Test if our error suppression works
            const originalConsoleError = console.error;
            let errorSuppressed = false;
            
            console.error = function(...args) {
                const message = args.join(' ');
                if (message.includes('message port closed')) {
                    errorSuppressed = true;
                    return;
                }
                originalConsoleError.apply(console, args);
            };
            
            console.error('Test: Unchecked runtime.lastError: The message port closed before a response was received');
            
            if (errorSuppressed) {
                updateStatus('extension-status', 'Error suppression working correctly', 'success');
                log('Error suppression test passed', 'success');
            } else {
                updateStatus('extension-status', 'Error suppression not working', 'error');
                log('Error suppression test failed', 'error');
            }
            
            console.error = originalConsoleError;
        }
        
        function simulateFirebaseTimeout() {
            log('Simulating Firebase timeout...', 'test');
            
            // Simulate Firebase taking too long
            window.firebaseInitialized = false;
            
            setTimeout(() => {
                updateStatus('firebase-status', 'Firebase timeout simulated', 'warning');
                log('Firebase timeout simulation complete', 'success');
            }, 1000);
        }
        
        function simulateFirebaseError() {
            log('Simulating Firebase error...', 'test');
            
            // Simulate Firebase initialization error
            window.dispatchEvent(new CustomEvent('unhandledrejection', {
                detail: { reason: new Error('Firebase initialization failed') }
            }));
            
            updateStatus('firebase-status', 'Firebase error simulated', 'warning');
            log('Firebase error simulation complete', 'success');
        }
        
        function createMockLoadingScreen() {
            log('Creating mock loading screen...', 'test');
            
            // Remove existing mock if any
            const existing = document.getElementById('loading-screen');
            if (existing) existing.remove();
            
            // Create mock loading screen
            const loadingScreen = document.createElement('div');
            loadingScreen.id = 'loading-screen';
            loadingScreen.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.8);
                display: flex;
                align-items: center;
                justify-content: center;
                z-index: 10000;
                color: white;
                font-size: 18px;
            `;
            loadingScreen.innerHTML = '<div>Mock Loading Screen...</div>';
            
            document.body.appendChild(loadingScreen);
            
            updateStatus('timeout-status', 'Mock loading screen created', 'success');
            log('Mock loading screen created', 'success');
        }
        
        function testLoadingScreenTimeout() {
            log('Testing loading screen timeout layers...', 'test');
            
            createMockLoadingScreen();
            
            // Test if our timeout mechanisms work
            let timeoutCount = 0;
            const originalSetTimeout = window.setTimeout;
            
            window.setTimeout = function(callback, delay) {
                if (delay <= 12000) { // Our timeout ranges
                    timeoutCount++;
                }
                return originalSetTimeout(callback, delay);
            };
            
            // Trigger timeout test
            setTimeout(() => {
                const loadingScreen = document.getElementById('loading-screen');
                if (!loadingScreen || loadingScreen.style.display === 'none') {
                    updateStatus('timeout-status', `Loading screen timeout working (${timeoutCount} timeouts set)`, 'success');
                    log(`Loading screen timeout test passed (${timeoutCount} timeouts)`, 'success');
                } else {
                    updateStatus('timeout-status', 'Loading screen timeout not working', 'error');
                    log('Loading screen timeout test failed', 'error');
                }
                
                window.setTimeout = originalSetTimeout;
            }, 2000);
        }
        
        function runAllTests() {
            log('Running all tests...', 'test');
            clearLog();
            
            setTimeout(() => testErrorSuppression(), 500);
            setTimeout(() => simulateExtensionError(), 1000);
            setTimeout(() => simulateFirebaseTimeout(), 1500);
            setTimeout(() => simulateFirebaseError(), 2000);
            setTimeout(() => testLoadingScreenTimeout(), 2500);
            
            setTimeout(() => {
                log('All tests completed', 'success');
            }, 5000);
        }
        
        // Initialize
        log('Test page loaded successfully', 'success');
    </script>
</body>
</html>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Constant-Time Operations - Forge EC Security</title>
    
    <!-- SEO Meta Tags -->
    <meta name="description" content="Understanding and implementing timing-attack resistant code with Forge EC. Learn about constant-time operations and side-channel attack prevention.">
    <meta name="keywords" content="forge ec, constant-time, timing attacks, side-channel, security, rust">
    <meta name="author" content="Tanmay Patil">
    
    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="Constant-Time Operations - Forge EC Security">
    <meta property="og:description" content="Timing-attack resistant cryptographic operations">
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://forge-ec.dev/docs/security/constant-time.html">
    
    <!-- Stylesheets -->
    <link rel="stylesheet" href="../../css/style.css">
    <link rel="stylesheet" href="../../css/animations.css">
    <link rel="stylesheet" href="../../css/components.css">
    <link rel="stylesheet" href="../docs.css">
    
    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="../../assets/favicon.svg">
    
    <!-- Syntax Highlighting -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/themes/prism-tomorrow.min.css">
</head>
<body>
    <!-- Loading Screen -->
    <div class="loading-screen" id="loading-screen">
        <div class="loading-content">
            <div class="loading-logo">
                <svg viewBox="0 0 40 40" xmlns="http://www.w3.org/2000/svg">
                    <path d="M8,20 Q20,8 32,20 Q20,32 8,20" stroke="currentColor" stroke-width="2" fill="none"/>
                    <circle cx="20" cy="20" r="2" fill="currentColor"/>
                </svg>
            </div>
            <div class="loading-text">Loading Constant-Time Operations Guide...</div>
            <div class="loading-progress">
                <div class="loading-bar" id="loading-bar"></div>
            </div>
        </div>
    </div>

    <!-- Navigation -->
    <nav class="navbar docs-navbar" id="navbar">
        <div class="nav-container">
            <div class="nav-brand">
                <a href="../../index.html" class="brand-link">
                    <svg class="brand-logo" viewBox="0 0 40 40" xmlns="http://www.w3.org/2000/svg">
                        <path d="M8,20 Q20,8 32,20 Q20,32 8,20" stroke="currentColor" stroke-width="2" fill="none"/>
                        <circle cx="20" cy="20" r="2" fill="currentColor"/>
                    </svg>
                    <span class="brand-text">Forge EC</span>
                </a>
            </div>

            <div class="nav-menu" id="nav-menu">
                <a href="../../index.html" class="nav-link">Home</a>
                <a href="../../index.html#features" class="nav-link">Features</a>
                <a href="../../index.html#about" class="nav-link">About</a>
                <a href="../index.html" class="nav-link active">Documentation</a>
                <a href="../../index.html#examples" class="nav-link">Examples</a>
                <a href="../../index.html#community" class="nav-link">Community</a>
                <a href="../../index.html#contact" class="nav-link">Contact</a>
            </div>

            <div class="nav-actions">
                <button class="theme-toggle" id="theme-toggle" aria-label="Toggle theme">
                    <svg class="theme-icon sun-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                        <circle cx="12" cy="12" r="5"/>
                        <path d="M12 1v2M12 21v2M4.22 4.22l1.42 1.42M18.36 18.36l1.42 1.42M1 12h2M21 12h2M4.22 19.78l1.42-1.42M18.36 5.64l1.42-1.42"/>
                    </svg>
                    <svg class="theme-icon moon-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                        <path d="M21 12.79A9 9 0 1 1 11.21 3 7 7 0 0 0 21 12.79z"/>
                    </svg>
                </button>
                <button class="auth-btn" id="auth-btn">
                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                        <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"/>
                        <circle cx="12" cy="7" r="4"/>
                    </svg>
                    <span>Sign In</span>
                </button>
                <button class="mobile-menu-toggle" id="mobile-menu-toggle" aria-label="Toggle mobile menu">
                    <span></span>
                    <span></span>
                    <span></span>
                </button>
            </div>
        </div>
    </nav>

    <!-- Documentation Content -->
    <main class="docs-main">
        <!-- Documentation Header -->
        <div class="docs-header">
            <div class="container">
                <div class="docs-header-content">
                    <div class="docs-meta">
                        <span class="docs-category">Security</span>
                        <span class="docs-level expert">Expert</span>
                        <span class="docs-time">15 min read</span>
                    </div>
                    <h1 class="docs-title">Constant-Time Operations</h1>
                    <p class="docs-subtitle">
                        Understanding and implementing timing-attack resistant code. Learn how to protect 
                        against side-channel attacks through constant-time cryptographic operations.
                    </p>
                    <div class="docs-actions">
                        <button class="bookmark-btn" id="bookmark-btn" aria-label="Bookmark this page">
                            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                <path d="M19 21l-7-5-7 5V5a2 2 0 0 1 2-2h10a2 2 0 0 1 2 2z"/>
                            </svg>
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Documentation Content -->
        <div class="docs-content">
            <div class="container">
                <div class="docs-layout">
                    <!-- Table of Contents -->
                    <aside class="docs-sidebar">
                        <div class="toc-container glass-enhanced">
                            <h3 class="toc-title">Table of Contents</h3>
                            <nav class="toc" id="toc">
                                <ul class="toc-list">
                                    <li class="toc-item">
                                        <a href="#overview" class="toc-link">Overview</a>
                                    </li>
                                    <li class="toc-item">
                                        <a href="#timing-attacks" class="toc-link">Timing Attacks</a>
                                    </li>
                                    <li class="toc-item">
                                        <a href="#constant-time-principles" class="toc-link">Constant-Time Principles</a>
                                    </li>
                                    <li class="toc-item">
                                        <a href="#implementation" class="toc-link">Implementation</a>
                                    </li>
                                    <li class="toc-item">
                                        <a href="#verification" class="toc-link">Verification</a>
                                    </li>
                                    <li class="toc-item">
                                        <a href="#examples" class="toc-link">Examples</a>
                                    </li>
                                    <li class="toc-item">
                                        <a href="#best-practices" class="toc-link">Best Practices</a>
                                    </li>
                                </ul>
                            </nav>
                        </div>
                    </aside>

                    <!-- Main Content -->
                    <article class="docs-article">
                        <section id="overview" class="docs-section">
                            <h2>Overview</h2>
                            <p>
                                Constant-time operations are crucial for preventing timing attacks in cryptographic 
                                implementations. Forge EC provides built-in constant-time operations and guidelines 
                                for maintaining timing-attack resistance in your applications.
                            </p>

                            <div class="warning-box">
                                <div class="warning-icon">
                                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                        <path d="M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z"/>
                                        <line x1="12" y1="9" x2="12" y2="13"/>
                                        <line x1="12" y1="17" x2="12.01" y2="17"/>
                                    </svg>
                                </div>
                                <div class="warning-content">
                                    <strong>Critical Security Requirement:</strong>
                                    <p>All cryptographic operations involving secret data must be implemented in constant time 
                                    to prevent timing attacks. This includes key operations, signature verification, 
                                    and any computation that depends on private keys or sensitive data.</p>
                                </div>
                            </div>
                        </section>

                        <section id="timing-attacks" class="docs-section">
                            <h2>Timing Attacks</h2>
                            <p>
                                Timing attacks exploit variations in execution time to extract information about secret data. 
                                Even microsecond differences can leak sensitive information over many observations.
                            </p>

                            <h3>Attack Vectors</h3>
                            <div class="code-block-container">
                                <div class="code-block-header">
                                    <span class="code-block-title">Vulnerable Code Example (DO NOT USE)</span>
                                    <button class="copy-btn" data-copy='// ❌ VULNERABLE: Early return leaks timing information
fn vulnerable_verify(signature: &[u8], expected: &[u8]) -> bool {
    if signature.len() != expected.len() {
        return false; // Early return - timing leak!
    }
    
    for (a, b) in signature.iter().zip(expected.iter()) {
        if a != b {
            return false; // Early return - timing leak!
        }
    }
    true
}

// ❌ VULNERABLE: Conditional operations leak timing
fn vulnerable_scalar_mult(scalar: &[u8], point: &Point) -> Point {
    let mut result = Point::identity();
    for bit in scalar.iter() {
        result = result.double();
        if *bit == 1 {
            result = result.add(point); // Conditional operation - timing leak!
        }
    }
    result
}'>
                                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                            <rect x="9" y="9" width="13" height="13" rx="2" ry="2"/>
                                            <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"/>
                                        </svg>
                                    </button>
                                </div>
                                <pre><code class="language-rust">// ❌ VULNERABLE: Early return leaks timing information
fn vulnerable_verify(signature: &[u8], expected: &[u8]) -> bool {
    if signature.len() != expected.len() {
        return false; // Early return - timing leak!
    }
    
    for (a, b) in signature.iter().zip(expected.iter()) {
        if a != b {
            return false; // Early return - timing leak!
        }
    }
    true
}

// ❌ VULNERABLE: Conditional operations leak timing
fn vulnerable_scalar_mult(scalar: &[u8], point: &Point) -> Point {
    let mut result = Point::identity();
    for bit in scalar.iter() {
        result = result.double();
        if *bit == 1 {
            result = result.add(point); // Conditional operation - timing leak!
        }
    }
    result
}</code></pre>
                            </div>
                        </section>

                        <section id="constant-time-principles" class="docs-section">
                            <h2>Constant-Time Principles</h2>
                            <p>
                                Constant-time implementations ensure that execution time is independent of secret data values.
                            </p>

                            <h3>Secure Implementation</h3>
                            <div class="code-block-container">
                                <div class="code-block-header">
                                    <span class="code-block-title">Constant-Time Code Example</span>
                                    <button class="copy-btn" data-copy='use forge_ec::subtle::{ConstantTimeEq, Choice};

// ✅ SECURE: Constant-time comparison
fn secure_verify(signature: &[u8], expected: &[u8]) -> bool {
    use forge_ec::subtle::ConstantTimeEq;
    
    // Always check full length, no early returns
    let len_match = signature.len() == expected.len();
    
    // Pad shorter array to avoid timing leaks
    let max_len = signature.len().max(expected.len());
    let mut sig_padded = vec![0u8; max_len];
    let mut exp_padded = vec![0u8; max_len];
    
    sig_padded[..signature.len()].copy_from_slice(signature);
    exp_padded[..expected.len()].copy_from_slice(expected);
    
    // Constant-time comparison
    let bytes_match = sig_padded.ct_eq(&exp_padded);
    
    len_match && bytes_match.into()
}

// ✅ SECURE: Constant-time scalar multiplication
fn secure_scalar_mult(scalar: &[u8], point: &Point) -> Point {
    let mut result = Point::identity();
    let mut addend = *point;
    
    for byte in scalar.iter() {
        for bit_index in 0..8 {
            let bit = (byte >> bit_index) & 1;
            let choice = Choice::from(bit);
            
            // Always perform both operations, select result
            let add_result = result.add(&addend);
            result = Point::conditional_select(&result, &add_result, choice);
            
            addend = addend.double();
        }
    }
    result
}'>
                                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                            <rect x="9" y="9" width="13" height="13" rx="2" ry="2"/>
                                            <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"/>
                                        </svg>
                                    </button>
                                </div>
                                <pre><code class="language-rust">use forge_ec::subtle::{ConstantTimeEq, Choice};

// ✅ SECURE: Constant-time comparison
fn secure_verify(signature: &[u8], expected: &[u8]) -> bool {
    use forge_ec::subtle::ConstantTimeEq;
    
    // Always check full length, no early returns
    let len_match = signature.len() == expected.len();
    
    // Pad shorter array to avoid timing leaks
    let max_len = signature.len().max(expected.len());
    let mut sig_padded = vec![0u8; max_len];
    let mut exp_padded = vec![0u8; max_len];
    
    sig_padded[..signature.len()].copy_from_slice(signature);
    exp_padded[..expected.len()].copy_from_slice(expected);
    
    // Constant-time comparison
    let bytes_match = sig_padded.ct_eq(&exp_padded);
    
    len_match && bytes_match.into()
}

// ✅ SECURE: Constant-time scalar multiplication
fn secure_scalar_mult(scalar: &[u8], point: &Point) -> Point {
    let mut result = Point::identity();
    let mut addend = *point;
    
    for byte in scalar.iter() {
        for bit_index in 0..8 {
            let bit = (byte >> bit_index) & 1;
            let choice = Choice::from(bit);
            
            // Always perform both operations, select result
            let add_result = result.add(&addend);
            result = Point::conditional_select(&result, &add_result, choice);
            
            addend = addend.double();
        }
    }
    result
}</code></pre>
                            </div>
                        </section>

                        <section id="implementation" class="docs-section">
                            <h2>Implementation Guidelines</h2>
                            <p>
                                Best practices for implementing constant-time operations in Forge EC applications.
                            </p>

                            <h3>Using Forge EC's Constant-Time Primitives</h3>
                            <div class="code-block-container">
                                <div class="code-block-header">
                                    <span class="code-block-title">Constant-Time Operations with Forge EC</span>
                                    <button class="copy-btn" data-copy='use forge_ec::{PrivateKey, PublicKey, Signature, subtle::ConstantTimeEq};

fn secure_signature_verification() -> Result<bool, Box<dyn std::error::Error>> {
    let message = b"Important message";
    let private_key = PrivateKey::new();
    let public_key = private_key.public_key();

    // Sign message
    let signature = private_key.sign(message)?;

    // ✅ SECURE: Constant-time verification
    let is_valid = public_key.verify_constant_time(message, &signature)?;

    // ✅ SECURE: Constant-time comparison of signatures
    let expected_signature = private_key.sign(message)?;
    let signatures_match = signature.ct_eq(&expected_signature).into();

    Ok(is_valid && signatures_match)
}'>
                                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                            <rect x="9" y="9" width="13" height="13" rx="2" ry="2"/>
                                            <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"/>
                                        </svg>
                                    </button>
                                </div>
                                <pre><code class="language-rust">use forge_ec::{PrivateKey, PublicKey, Signature, subtle::ConstantTimeEq};

fn secure_signature_verification() -> Result<bool, Box<dyn std::error::Error>> {
    let message = b"Important message";
    let private_key = PrivateKey::new();
    let public_key = private_key.public_key();

    // Sign message
    let signature = private_key.sign(message)?;

    // ✅ SECURE: Constant-time verification
    let is_valid = public_key.verify_constant_time(message, &signature)?;

    // ✅ SECURE: Constant-time comparison of signatures
    let expected_signature = private_key.sign(message)?;
    let signatures_match = signature.ct_eq(&expected_signature).into();

    Ok(is_valid && signatures_match)
}</code></pre>
                            </div>
                        </section>

                        <section id="verification" class="docs-section">
                            <h2>Verification Techniques</h2>
                            <p>
                                Methods to verify that your implementations are truly constant-time.
                            </p>

                            <h3>Testing for Timing Leaks</h3>
                            <div class="code-block-container">
                                <div class="code-block-header">
                                    <span class="code-block-title">Timing Analysis Test</span>
                                    <button class="copy-btn" data-copy='use std::time::Instant;
use forge_ec::{PrivateKey, subtle::ConstantTimeEq};

#[cfg(test)]
mod timing_tests {
    use super::*;

    #[test]
    fn test_constant_time_comparison() {
        let key1 = PrivateKey::new();
        let key2 = PrivateKey::new();

        let mut times_equal = Vec::new();
        let mut times_different = Vec::new();

        // Test equal keys
        for _ in 0..1000 {
            let start = Instant::now();
            let _ = key1.to_bytes().ct_eq(&key1.to_bytes());
            times_equal.push(start.elapsed().as_nanos());
        }

        // Test different keys
        for _ in 0..1000 {
            let start = Instant::now();
            let _ = key1.to_bytes().ct_eq(&key2.to_bytes());
            times_different.push(start.elapsed().as_nanos());
        }

        // Statistical analysis
        let avg_equal: f64 = times_equal.iter().sum::<u128>() as f64 / times_equal.len() as f64;
        let avg_different: f64 = times_different.iter().sum::<u128>() as f64 / times_different.len() as f64;

        // Times should be statistically similar
        let difference_ratio = (avg_equal - avg_different).abs() / avg_equal.max(avg_different);
        assert!(difference_ratio < 0.1, "Timing difference too large: {}", difference_ratio);
    }
}'>
                                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                            <rect x="9" y="9" width="13" height="13" rx="2" ry="2"/>
                                            <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"/>
                                        </svg>
                                    </button>
                                </div>
                                <pre><code class="language-rust">use std::time::Instant;
use forge_ec::{PrivateKey, subtle::ConstantTimeEq};

#[cfg(test)]
mod timing_tests {
    use super::*;

    #[test]
    fn test_constant_time_comparison() {
        let key1 = PrivateKey::new();
        let key2 = PrivateKey::new();

        let mut times_equal = Vec::new();
        let mut times_different = Vec::new();

        // Test equal keys
        for _ in 0..1000 {
            let start = Instant::now();
            let _ = key1.to_bytes().ct_eq(&key1.to_bytes());
            times_equal.push(start.elapsed().as_nanos());
        }

        // Test different keys
        for _ in 0..1000 {
            let start = Instant::now();
            let _ = key1.to_bytes().ct_eq(&key2.to_bytes());
            times_different.push(start.elapsed().as_nanos());
        }

        // Statistical analysis
        let avg_equal: f64 = times_equal.iter().sum::<u128>() as f64 / times_equal.len() as f64;
        let avg_different: f64 = times_different.iter().sum::<u128>() as f64 / times_different.len() as f64;

        // Times should be statistically similar
        let difference_ratio = (avg_equal - avg_different).abs() / avg_equal.max(avg_different);
        assert!(difference_ratio < 0.1, "Timing difference too large: {}", difference_ratio);
    }
}</code></pre>
                            </div>
                        </section>

                        <section id="examples" class="docs-section">
                            <h2>Complete Examples</h2>
                            <p>
                                Real-world examples of constant-time implementations.
                            </p>

                            <h3>Secure Authentication System</h3>
                            <div class="code-block-container">
                                <div class="code-block-header">
                                    <span class="code-block-title">Constant-Time Authentication</span>
                                    <button class="copy-btn" data-copy='use forge_ec::{PrivateKey, PublicKey, Signature, subtle::ConstantTimeEq};
use std::collections::HashMap;

pub struct SecureAuthenticator {
    users: HashMap<String, PublicKey>,
}

impl SecureAuthenticator {
    pub fn new() -> Self {
        Self {
            users: HashMap::new(),
        }
    }

    pub fn register_user(&mut self, username: String, public_key: PublicKey) {
        self.users.insert(username, public_key);
    }

    // ✅ SECURE: Constant-time authentication
    pub fn authenticate(&self, username: &str, message: &[u8], signature: &Signature) -> bool {
        // Always perform verification, even if user doesn't exist
        let dummy_key = PublicKey::default();
        let (key_to_use, user_exists) = match self.users.get(username) {
            Some(key) => (key, true),
            None => (&dummy_key, false),
        };

        // Always verify signature (constant-time)
        let signature_valid = key_to_use
            .verify_constant_time(message, signature)
            .unwrap_or(false);

        // Return result only if both user exists and signature is valid
        user_exists && signature_valid
    }
}'>
                                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                            <rect x="9" y="9" width="13" height="13" rx="2" ry="2"/>
                                            <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"/>
                                        </svg>
                                    </button>
                                </div>
                                <pre><code class="language-rust">use forge_ec::{PrivateKey, PublicKey, Signature, subtle::ConstantTimeEq};
use std::collections::HashMap;

pub struct SecureAuthenticator {
    users: HashMap<String, PublicKey>,
}

impl SecureAuthenticator {
    pub fn new() -> Self {
        Self {
            users: HashMap::new(),
        }
    }

    pub fn register_user(&mut self, username: String, public_key: PublicKey) {
        self.users.insert(username, public_key);
    }

    // ✅ SECURE: Constant-time authentication
    pub fn authenticate(&self, username: &str, message: &[u8], signature: &Signature) -> bool {
        // Always perform verification, even if user doesn't exist
        let dummy_key = PublicKey::default();
        let (key_to_use, user_exists) = match self.users.get(username) {
            Some(key) => (key, true),
            None => (&dummy_key, false),
        };

        // Always verify signature (constant-time)
        let signature_valid = key_to_use
            .verify_constant_time(message, signature)
            .unwrap_or(false);

        // Return result only if both user exists and signature is valid
        user_exists && signature_valid
    }
}</code></pre>
                            </div>
                        </section>

                        <section id="best-practices" class="docs-section">
                            <h2>Best Practices</h2>
                            <p>
                                Essential guidelines for maintaining constant-time security.
                            </p>

                            <div class="info-box">
                                <div class="info-icon">
                                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                        <circle cx="12" cy="12" r="10"/>
                                        <path d="M12 16v-4M12 8h.01"/>
                                    </svg>
                                </div>
                                <div class="info-content">
                                    <strong>Constant-Time Checklist:</strong>
                                    <ul>
                                        <li>✅ Use Forge EC's built-in constant-time operations</li>
                                        <li>✅ Avoid conditional branches on secret data</li>
                                        <li>✅ Use constant-time comparison functions</li>
                                        <li>✅ Implement dummy operations for timing consistency</li>
                                        <li>✅ Test for timing leaks with statistical analysis</li>
                                        <li>✅ Clear sensitive data from memory securely</li>
                                    </ul>
                                </div>
                            </div>

                            <h3>Common Pitfalls to Avoid</h3>
                            <ul class="docs-list">
                                <li><strong>Early returns:</strong> Never return early based on secret data</li>
                                <li><strong>Conditional operations:</strong> Avoid if/else branches on secrets</li>
                                <li><strong>Variable-time algorithms:</strong> Use constant-time alternatives</li>
                                <li><strong>Memory access patterns:</strong> Ensure consistent memory access</li>
                                <li><strong>Compiler optimizations:</strong> Be aware of optimization effects</li>
                            </ul>
                        </section>
                    </article>
                </div>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="docs-footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <p>&copy; 2024 Forge EC. Built with ❤️ by <a href="https://github.com/tanm-sys" target="_blank">Tanmay Patil</a></p>
                </div>
                <div class="footer-section">
                    <div class="footer-links">
                        <a href="../../index.html#about">About</a>
                        <a href="../../index.html#community">Community</a>
                        <a href="guidelines.html">Security</a>
                        <a href="https://github.com/tanm-sys/forge-ec" target="_blank">GitHub</a>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <!-- Firebase Configuration -->
    <script type="module">
        // Suppress browser extension errors that don't affect functionality
        const originalConsoleError = console.error;
        console.error = function(...args) {
            const message = args.join(' ');
            // Filter out known browser extension errors
            if (message.includes('message port closed') ||
                message.includes('Extension context invalidated') ||
                message.includes('runtime.lastError')) {
                return; // Suppress these harmless extension errors
            }
            originalConsoleError.apply(console, args);
        };

        try {
            // Firebase CDN imports with timeout protection
            const importTimeout = setTimeout(() => {
                console.warn('Firebase import timeout - continuing without Firebase');
                window.firebaseInitialized = false;
                window.dispatchEvent(new CustomEvent('firebaseReady'));
            }, 5000);

            Promise.all([
                import('https://www.gstatic.com/firebasejs/11.8.1/firebase-app.js'),
                import('https://www.gstatic.com/firebasejs/11.8.1/firebase-firestore.js'),
                import('https://www.gstatic.com/firebasejs/11.8.1/firebase-auth.js')
            ]).then(([appModule, firestoreModule, authModule]) => {
                clearTimeout(importTimeout);

                // Firebase configuration
                const firebaseConfig = {
                    apiKey: "AIzaSyDBG9YcnodA8Lhpwb3wOoyp93VcqXygcrQ",
                    authDomain: "forge-ec.firebaseapp.com",
                    databaseURL: "https://forge-ec-default-rtdb.firebaseio.com",
                    projectId: "forge-ec",
                    storageBucket: "forge-ec.firebasestorage.app",
                    messagingSenderId: "436060720516",
                    appId: "1:436060720516:web:4c4ac16371db82fcfd61d1",
                    measurementId: "G-1BVB7FLGRJ"
                };

                // Initialize Firebase
                const app = appModule.initializeApp(firebaseConfig);
                const db = firestoreModule.getFirestore(app);
                const auth = authModule.getAuth(app);

                // Make Firebase services globally available
                window.firebaseApp = app;
                window.firebaseDb = db;
                window.firebaseAuth = auth;
                window.firebaseInitialized = true;

                // Dispatch ready event
                window.dispatchEvent(new CustomEvent('firebaseReady'));
                console.log('✅ Firebase initialized successfully');
            }).catch(error => {
                clearTimeout(importTimeout);
                console.warn('Firebase initialization failed:', error);
                window.firebaseInitialized = false;
                window.dispatchEvent(new CustomEvent('firebaseReady'));
            });
        } catch (error) {
            console.warn('Firebase setup failed:', error);
            window.firebaseInitialized = false;
            window.dispatchEvent(new CustomEvent('firebaseReady'));
        }
    </script>

    <!-- Scripts -->
    <script src="../docs.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/components/prism-core.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/plugins/autoloader/prism-autoloader.min.js"></script>

    <!-- Unified Loading Screen Management -->
    <script>
        // Check if global LoadingScreenManager is available, otherwise use fallback
        function initializeLoadingScreenProtection() {
            // If docs.js LoadingScreenManager is available, it will handle everything
            if (window.LoadingScreenManager) {
                console.log('🔄 Using global LoadingScreenManager');
                return;
            }

            // Fallback protection if docs.js fails to load
            console.log('🔄 Using fallback loading screen protection');

            let fallbackHidden = false;
            function fallbackHide(reason) {
                if (fallbackHidden || window.loadingScreenHidden) return;

                const loadingScreen = document.getElementById('loading-screen');
                if (loadingScreen) {
                    console.log(`🔄 Fallback hiding loading screen: ${reason}`);
                    fallbackHidden = true;
                    window.loadingScreenHidden = true;

                    loadingScreen.style.opacity = '0';
                    loadingScreen.style.pointerEvents = 'none';
                    loadingScreen.style.transition = 'opacity 0.3s ease-out';

                    setTimeout(() => {
                        if (loadingScreen.parentNode) {
                            loadingScreen.style.display = 'none';
                            setTimeout(() => {
                                if (loadingScreen.parentNode) {
                                    loadingScreen.remove();
                                }
                            }, 100);
                        }
                    }, 300);
                }
            }

            // Fallback timeouts
            setTimeout(() => fallbackHide('Fallback timeout (3s)'), 3000);
            setTimeout(() => fallbackHide('Fallback timeout (6s)'), 6000);

            // Fallback event listeners
            window.addEventListener('load', () => setTimeout(() => fallbackHide('Fallback page load'), 500));
            document.addEventListener('DOMContentLoaded', () => setTimeout(() => fallbackHide('Fallback DOM loaded'), 1000));

            ['click', 'keydown', 'touchstart'].forEach(event => {
                document.addEventListener(event, () => fallbackHide('Fallback user interaction'), { once: true });
            });
        }

        // Initialize immediately
        initializeLoadingScreenProtection();

        // Also try after a short delay in case docs.js loads later
        setTimeout(initializeLoadingScreenProtection, 100);
    </script>
</body>
</html>

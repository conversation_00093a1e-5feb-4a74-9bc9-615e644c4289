<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Encoding Module - Forge EC API Reference</title>
    
    <!-- SEO Meta Tags -->
    <meta name="description" content="Complete API reference for Forge EC Encoding module. Point compression, serialization, and format conversion functions.">
    <meta name="keywords" content="forge ec, encoding, point compression, serialization, rust, API">
    <meta name="author" content="Tanmay Patil">
    
    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="Encoding Module - Forge EC API Reference">
    <meta property="og:description" content="Point compression, serialization, and format conversion API">
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://forge-ec.dev/docs/api/encoding.html">
    
    <!-- Stylesheets -->
    <link rel="stylesheet" href="../../css/style.css">
    <link rel="stylesheet" href="../../css/animations.css">
    <link rel="stylesheet" href="../../css/components.css">
    <link rel="stylesheet" href="../docs.css">
    
    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="../../assets/favicon.svg">
    
    <!-- Syntax Highlighting -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/themes/prism-tomorrow.min.css">
</head>
<body>
    <!-- Loading Screen -->
    <div class="loading-screen" id="loading-screen">
        <div class="loading-content">
            <div class="loading-logo">
                <svg viewBox="0 0 40 40" xmlns="http://www.w3.org/2000/svg">
                    <path d="M8,20 Q20,8 32,20 Q20,32 8,20" stroke="currentColor" stroke-width="2" fill="none"/>
                    <circle cx="20" cy="20" r="2" fill="currentColor"/>
                </svg>
            </div>
            <div class="loading-text">Loading Encoding API Reference...</div>
            <div class="loading-progress">
                <div class="loading-bar" id="loading-bar"></div>
            </div>
        </div>
    </div>

    <!-- Navigation -->
    <nav class="navbar docs-navbar" id="navbar">
        <div class="nav-container">
            <div class="nav-brand">
                <a href="../../index.html" class="brand-link">
                    <svg class="brand-logo" viewBox="0 0 40 40" xmlns="http://www.w3.org/2000/svg">
                        <path d="M8,20 Q20,8 32,20 Q20,32 8,20" stroke="currentColor" stroke-width="2" fill="none"/>
                        <circle cx="20" cy="20" r="2" fill="currentColor"/>
                    </svg>
                    <span class="brand-text">Forge EC</span>
                </a>
            </div>

            <div class="nav-menu" id="nav-menu">
                <a href="../../index.html" class="nav-link">Home</a>
                <a href="../../index.html#features" class="nav-link">Features</a>
                <a href="../../index.html#about" class="nav-link">About</a>
                <a href="../index.html" class="nav-link active">Documentation</a>
                <a href="../../index.html#examples" class="nav-link">Examples</a>
                <a href="../../index.html#community" class="nav-link">Community</a>
                <a href="../../index.html#contact" class="nav-link">Contact</a>
            </div>

            <div class="nav-actions">
                <button class="theme-toggle" id="theme-toggle" aria-label="Toggle theme">
                    <svg class="theme-icon sun-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                        <circle cx="12" cy="12" r="5"/>
                        <path d="M12 1v2M12 21v2M4.22 4.22l1.42 1.42M18.36 18.36l1.42 1.42M1 12h2M21 12h2M4.22 19.78l1.42-1.42M18.36 5.64l1.42-1.42"/>
                    </svg>
                    <svg class="theme-icon moon-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                        <path d="M21 12.79A9 9 0 1 1 11.21 3 7 7 0 0 0 21 12.79z"/>
                    </svg>
                </button>
                <button class="auth-btn" id="auth-btn">
                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                        <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"/>
                        <circle cx="12" cy="7" r="4"/>
                    </svg>
                    <span>Sign In</span>
                </button>
                <button class="mobile-menu-toggle" id="mobile-menu-toggle" aria-label="Toggle mobile menu">
                    <span></span>
                    <span></span>
                    <span></span>
                </button>
            </div>
        </div>
    </nav>

    <!-- Documentation Content -->
    <main class="docs-main">
        <!-- Documentation Header -->
        <div class="docs-header">
            <div class="container">
                <div class="docs-header-content">
                    <div class="docs-meta">
                        <span class="docs-category">API Reference</span>
                        <span class="docs-level intermediate">Intermediate</span>
                        <span class="docs-time">12 min read</span>
                    </div>
                    <h1 class="docs-title">Encoding Module</h1>
                    <p class="docs-subtitle">
                        Point compression, serialization, and format conversion functions for elliptic curve points and keys. 
                        Efficient encoding schemes for storage and transmission.
                    </p>
                    <div class="docs-actions">
                        <button class="bookmark-btn" id="bookmark-btn" aria-label="Bookmark this page">
                            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                <path d="M19 21l-7-5-7 5V5a2 2 0 0 1 2-2h10a2 2 0 0 1 2 2z"/>
                            </svg>
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Documentation Content -->
        <div class="docs-content">
            <div class="container">
                <div class="docs-layout">
                    <!-- Table of Contents -->
                    <aside class="docs-sidebar">
                        <div class="toc-container glass-enhanced">
                            <h3 class="toc-title">Table of Contents</h3>
                            <nav class="toc" id="toc">
                                <ul class="toc-list">
                                    <li class="toc-item">
                                        <a href="#overview" class="toc-link">Overview</a>
                                    </li>
                                    <li class="toc-item">
                                        <a href="#point-compression" class="toc-link">Point Compression</a>
                                    </li>
                                    <li class="toc-item">
                                        <a href="#serialization" class="toc-link">Serialization</a>
                                    </li>
                                    <li class="toc-item">
                                        <a href="#format-conversion" class="toc-link">Format Conversion</a>
                                    </li>
                                    <li class="toc-item">
                                        <a href="#base64-encoding" class="toc-link">Base64 Encoding</a>
                                    </li>
                                    <li class="toc-item">
                                        <a href="#hex-encoding" class="toc-link">Hex Encoding</a>
                                    </li>
                                    <li class="toc-item">
                                        <a href="#der-encoding" class="toc-link">DER Encoding</a>
                                    </li>
                                    <li class="toc-item">
                                        <a href="#examples" class="toc-link">Examples</a>
                                    </li>
                                    <li class="toc-item">
                                        <a href="#error-handling" class="toc-link">Error Handling</a>
                                    </li>
                                </ul>
                            </nav>
                        </div>
                    </aside>

                    <!-- Main Content -->
                    <article class="docs-article">
                        <section id="overview" class="docs-section">
                            <h2>Overview</h2>
                            <p>
                                The Encoding module provides comprehensive functions for encoding and decoding elliptic curve 
                                points, keys, and signatures in various formats. It supports point compression, multiple 
                                serialization formats, and efficient conversion between different representations.
                            </p>

                            <div class="info-box">
                                <div class="info-icon">
                                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                        <circle cx="12" cy="12" r="10"/>
                                        <path d="M12 16v-4M12 8h.01"/>
                                    </svg>
                                </div>
                                <div class="info-content">
                                    <strong>Key Features:</strong>
                                    <ul>
                                        <li>Point compression and decompression</li>
                                        <li>Multiple serialization formats (binary, hex, base64)</li>
                                        <li>DER/PEM encoding for standards compliance</li>
                                        <li>Efficient memory usage and performance</li>
                                        <li>Constant-time operations for security</li>
                                    </ul>
                                </div>
                            </div>
                        </section>

                        <section id="point-compression" class="docs-section">
                            <h2>Point Compression</h2>
                            <p>
                                Point compression reduces the size of elliptic curve points by storing only the x-coordinate 
                                and a single bit indicating the y-coordinate's parity.
                            </p>

                            <h3>Compress Point</h3>
                            <div class="code-block-container">
                                <div class="code-block-header">
                                    <span class="code-block-title">Point Compression Example</span>
                                    <button class="copy-btn" data-copy='use forge_ec::{Point, encoding::compress_point};

fn compress_example() -> Result<(), Box<dyn std::error::Error>> {
    // Create a point (example coordinates)
    let point = Point::new(
        "0x79BE667EF9DCBBAC55A06295CE870B07029BFCDB2DCE28D959F2815B16F81798",
        "0x483ADA7726A3C4655DA4FBFC0E1108A8FD17B448A68554199C47D08FFB10D4B8"
    )?;

    // Compress the point (33 bytes instead of 65)
    let compressed = compress_point(&point)?;
    println!("Compressed point: {} bytes", compressed.len());

    // Decompress back to original point
    let decompressed = decompress_point(&compressed)?;
    assert_eq!(point, decompressed);

    Ok(())
}'>
                                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                            <rect x="9" y="9" width="13" height="13" rx="2" ry="2"/>
                                            <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"/>
                                        </svg>
                                    </button>
                                </div>
                                <pre><code class="language-rust">use forge_ec::{Point, encoding::compress_point};

fn compress_example() -> Result<(), Box<dyn std::error::Error>> {
    // Create a point (example coordinates)
    let point = Point::new(
        "0x79BE667EF9DCBBAC55A06295CE870B07029BFCDB2DCE28D959F2815B16F81798",
        "0x483ADA7726A3C4655DA4FBFC0E1108A8FD17B448A68554199C47D08FFB10D4B8"
    )?;

    // Compress the point (33 bytes instead of 65)
    let compressed = compress_point(&point)?;
    println!("Compressed point: {} bytes", compressed.len());

    // Decompress back to original point
    let decompressed = decompress_point(&compressed)?;
    assert_eq!(point, decompressed);

    Ok(())
}</code></pre>
                            </div>
                        </section>

                        <section id="serialization" class="docs-section">
                            <h2>Serialization</h2>
                            <p>
                                Serialize elliptic curve objects to binary formats for storage and transmission.
                            </p>

                            <h3>Binary Serialization</h3>
                            <div class="code-block-container">
                                <div class="code-block-header">
                                    <span class="code-block-title">Binary Serialization Example</span>
                                    <button class="copy-btn" data-copy='use forge_ec::{PrivateKey, PublicKey, encoding::{serialize, deserialize}};

fn serialization_example() -> Result<(), Box<dyn std::error::Error>> {
    // Generate a key pair
    let private_key = PrivateKey::new();
    let public_key = private_key.public_key();

    // Serialize private key (32 bytes)
    let private_bytes = serialize(&private_key)?;
    println!("Private key: {} bytes", private_bytes.len());

    // Serialize public key (compressed: 33 bytes, uncompressed: 65 bytes)
    let public_bytes = serialize(&public_key)?;
    println!("Public key: {} bytes", public_bytes.len());

    // Deserialize back
    let restored_private: PrivateKey = deserialize(&private_bytes)?;
    let restored_public: PublicKey = deserialize(&public_bytes)?;

    // Verify they match
    assert_eq!(private_key.to_bytes(), restored_private.to_bytes());
    assert_eq!(public_key.to_bytes(), restored_public.to_bytes());

    Ok(())
}'>
                                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                            <rect x="9" y="9" width="13" height="13" rx="2" ry="2"/>
                                            <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"/>
                                        </svg>
                                    </button>
                                </div>
                                <pre><code class="language-rust">use forge_ec::{PrivateKey, PublicKey, encoding::{serialize, deserialize}};

fn serialization_example() -> Result<(), Box<dyn std::error::Error>> {
    // Generate a key pair
    let private_key = PrivateKey::new();
    let public_key = private_key.public_key();

    // Serialize private key (32 bytes)
    let private_bytes = serialize(&private_key)?;
    println!("Private key: {} bytes", private_bytes.len());

    // Serialize public key (compressed: 33 bytes, uncompressed: 65 bytes)
    let public_bytes = serialize(&public_key)?;
    println!("Public key: {} bytes", public_bytes.len());

    // Deserialize back
    let restored_private: PrivateKey = deserialize(&private_bytes)?;
    let restored_public: PublicKey = deserialize(&public_bytes)?;

    // Verify they match
    assert_eq!(private_key.to_bytes(), restored_private.to_bytes());
    assert_eq!(public_key.to_bytes(), restored_public.to_bytes());

    Ok(())
}</code></pre>
                            </div>
                        </section>

                        <section id="format-conversion" class="docs-section">
                            <h2>Format Conversion</h2>
                            <p>
                                Convert between different encoding formats for interoperability.
                            </p>

                            <h3>Format Conversion Functions</h3>
                            <div class="code-block-container">
                                <div class="code-block-header">
                                    <span class="code-block-title">Format Conversion Example</span>
                                    <button class="copy-btn" data-copy='use forge_ec::{PublicKey, encoding::{to_hex, from_hex, to_base64, from_base64}};

fn format_conversion_example() -> Result<(), Box<dyn std::error::Error>> {
    let public_key = PublicKey::from_hex(
        "0279BE667EF9DCBBAC55A06295CE870B07029BFCDB2DCE28D959F2815B16F81798"
    )?;

    // Convert to different formats
    let hex_string = to_hex(&public_key)?;
    let base64_string = to_base64(&public_key)?;

    println!("Hex: {}", hex_string);
    println!("Base64: {}", base64_string);

    // Convert back from formats
    let from_hex_key: PublicKey = from_hex(&hex_string)?;
    let from_base64_key: PublicKey = from_base64(&base64_string)?;

    // Verify conversions are lossless
    assert_eq!(public_key.to_bytes(), from_hex_key.to_bytes());
    assert_eq!(public_key.to_bytes(), from_base64_key.to_bytes());

    Ok(())
}'>
                                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                            <rect x="9" y="9" width="13" height="13" rx="2" ry="2"/>
                                            <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"/>
                                        </svg>
                                    </button>
                                </div>
                                <pre><code class="language-rust">use forge_ec::{PublicKey, encoding::{to_hex, from_hex, to_base64, from_base64}};

fn format_conversion_example() -> Result<(), Box<dyn std::error::Error>> {
    let public_key = PublicKey::from_hex(
        "0279BE667EF9DCBBAC55A06295CE870B07029BFCDB2DCE28D959F2815B16F81798"
    )?;

    // Convert to different formats
    let hex_string = to_hex(&public_key)?;
    let base64_string = to_base64(&public_key)?;

    println!("Hex: {}", hex_string);
    println!("Base64: {}", base64_string);

    // Convert back from formats
    let from_hex_key: PublicKey = from_hex(&hex_string)?;
    let from_base64_key: PublicKey = from_base64(&base64_string)?;

    // Verify conversions are lossless
    assert_eq!(public_key.to_bytes(), from_hex_key.to_bytes());
    assert_eq!(public_key.to_bytes(), from_base64_key.to_bytes());

    Ok(())
}</code></pre>
                            </div>
                        </section>

                        <section id="base64-encoding" class="docs-section">
                            <h2>Base64 Encoding</h2>
                            <p>
                                Base64 encoding for text-safe representation of binary data.
                            </p>

                            <div class="code-block-container">
                                <div class="code-block-header">
                                    <span class="code-block-title">Base64 Encoding Example</span>
                                    <button class="copy-btn" data-copy='use forge_ec::{Signature, encoding::base64};

fn base64_example() -> Result<(), Box<dyn std::error::Error>> {
    // Create a signature (example)
    let signature = Signature::from_der(&signature_bytes)?;

    // Encode to Base64
    let base64_encoded = base64::encode(&signature)?;
    println!("Base64 signature: {}", base64_encoded);

    // Decode from Base64
    let decoded_signature = base64::decode(&base64_encoded)?;
    let restored_signature = Signature::from_bytes(&decoded_signature)?;

    // Verify integrity
    assert_eq!(signature.to_bytes(), restored_signature.to_bytes());

    // URL-safe Base64 encoding
    let url_safe_encoded = base64::encode_url_safe(&signature)?;
    println!("URL-safe Base64: {}", url_safe_encoded);

    Ok(())
}'>
                                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                            <rect x="9" y="9" width="13" height="13" rx="2" ry="2"/>
                                            <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"/>
                                        </svg>
                                    </button>
                                </div>
                                <pre><code class="language-rust">use forge_ec::{Signature, encoding::base64};

fn base64_example() -> Result<(), Box<dyn std::error::Error>> {
    // Create a signature (example)
    let signature = Signature::from_der(&signature_bytes)?;

    // Encode to Base64
    let base64_encoded = base64::encode(&signature)?;
    println!("Base64 signature: {}", base64_encoded);

    // Decode from Base64
    let decoded_signature = base64::decode(&base64_encoded)?;
    let restored_signature = Signature::from_bytes(&decoded_signature)?;

    // Verify integrity
    assert_eq!(signature.to_bytes(), restored_signature.to_bytes());

    // URL-safe Base64 encoding
    let url_safe_encoded = base64::encode_url_safe(&signature)?;
    println!("URL-safe Base64: {}", url_safe_encoded);

    Ok(())
}</code></pre>
                            </div>
                        </section>

                        <section id="hex-encoding" class="docs-section">
                            <h2>Hex Encoding</h2>
                            <p>
                                Hexadecimal encoding for human-readable representation of binary data.
                            </p>

                            <div class="code-block-container">
                                <div class="code-block-header">
                                    <span class="code-block-title">Hex Encoding Example</span>
                                    <button class="copy-btn" data-copy='use forge_ec::{PrivateKey, encoding::hex};

fn hex_example() -> Result<(), Box<dyn std::error::Error>> {
    // Generate a private key
    let private_key = PrivateKey::new();

    // Encode to hex (lowercase)
    let hex_lower = hex::encode(&private_key)?;
    println!("Hex (lower): {}", hex_lower);

    // Encode to hex (uppercase)
    let hex_upper = hex::encode_upper(&private_key)?;
    println!("Hex (upper): {}", hex_upper);

    // Decode from hex (case-insensitive)
    let decoded_key = hex::decode(&hex_lower)?;
    let restored_key = PrivateKey::from_bytes(&decoded_key)?;

    // Verify integrity
    assert_eq!(private_key.to_bytes(), restored_key.to_bytes());

    // With 0x prefix
    let hex_with_prefix = hex::encode_with_prefix(&private_key)?;
    println!("Hex with prefix: {}", hex_with_prefix);

    Ok(())
}'>
                                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                            <rect x="9" y="9" width="13" height="13" rx="2" ry="2"/>
                                            <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"/>
                                        </svg>
                                    </button>
                                </div>
                                <pre><code class="language-rust">use forge_ec::{PrivateKey, encoding::hex};

fn hex_example() -> Result<(), Box<dyn std::error::Error>> {
    // Generate a private key
    let private_key = PrivateKey::new();

    // Encode to hex (lowercase)
    let hex_lower = hex::encode(&private_key)?;
    println!("Hex (lower): {}", hex_lower);

    // Encode to hex (uppercase)
    let hex_upper = hex::encode_upper(&private_key)?;
    println!("Hex (upper): {}", hex_upper);

    // Decode from hex (case-insensitive)
    let decoded_key = hex::decode(&hex_lower)?;
    let restored_key = PrivateKey::from_bytes(&decoded_key)?;

    // Verify integrity
    assert_eq!(private_key.to_bytes(), restored_key.to_bytes());

    // With 0x prefix
    let hex_with_prefix = hex::encode_with_prefix(&private_key)?;
    println!("Hex with prefix: {}", hex_with_prefix);

    Ok(())
}</code></pre>
                            </div>
                        </section>

                        <section id="der-encoding" class="docs-section">
                            <h2>DER Encoding</h2>
                            <p>
                                Distinguished Encoding Rules (DER) for standards-compliant encoding of cryptographic objects.
                            </p>

                            <div class="code-block-container">
                                <div class="code-block-header">
                                    <span class="code-block-title">DER Encoding Example</span>
                                    <button class="copy-btn" data-copy='use forge_ec::{PublicKey, Signature, encoding::der};

fn der_example() -> Result<(), Box<dyn std::error::Error>> {
    let public_key = PublicKey::new();

    // Encode public key to DER format
    let der_encoded = der::encode_public_key(&public_key)?;
    println!("DER encoded public key: {} bytes", der_encoded.len());

    // Decode from DER
    let decoded_key = der::decode_public_key(&der_encoded)?;
    assert_eq!(public_key.to_bytes(), decoded_key.to_bytes());

    // DER encoding for signatures
    let signature = Signature::new(r_bytes, s_bytes)?;
    let der_signature = der::encode_signature(&signature)?;

    // This is compatible with OpenSSL and other standard libraries
    println!("DER signature: {} bytes", der_signature.len());

    Ok(())
}'>
                                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                            <rect x="9" y="9" width="13" height="13" rx="2" ry="2"/>
                                            <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"/>
                                        </svg>
                                    </button>
                                </div>
                                <pre><code class="language-rust">use forge_ec::{PublicKey, Signature, encoding::der};

fn der_example() -> Result<(), Box<dyn std::error::Error>> {
    let public_key = PublicKey::new();

    // Encode public key to DER format
    let der_encoded = der::encode_public_key(&public_key)?;
    println!("DER encoded public key: {} bytes", der_encoded.len());

    // Decode from DER
    let decoded_key = der::decode_public_key(&der_encoded)?;
    assert_eq!(public_key.to_bytes(), decoded_key.to_bytes());

    // DER encoding for signatures
    let signature = Signature::new(r_bytes, s_bytes)?;
    let der_signature = der::encode_signature(&signature)?;

    // This is compatible with OpenSSL and other standard libraries
    println!("DER signature: {} bytes", der_signature.len());

    Ok(())
}</code></pre>
                            </div>
                        </section>

                        <section id="examples" class="docs-section">
                            <h2>Complete Examples</h2>
                            <p>
                                Comprehensive examples demonstrating real-world usage of the encoding module.
                            </p>

                            <h3>Key Storage and Retrieval</h3>
                            <div class="code-block-container">
                                <div class="code-block-header">
                                    <span class="code-block-title">Key Storage Example</span>
                                    <button class="copy-btn" data-copy='use forge_ec::{PrivateKey, PublicKey, encoding::{hex, base64}};
use std::fs;

fn key_storage_example() -> Result<(), Box<dyn std::error::Error>> {
    // Generate a key pair
    let private_key = PrivateKey::new();
    let public_key = private_key.public_key();

    // Store private key securely (hex format)
    let private_hex = hex::encode(&private_key)?;
    fs::write("private_key.hex", private_hex)?;

    // Store public key (base64 format for sharing)
    let public_b64 = base64::encode(&public_key)?;
    fs::write("public_key.b64", public_b64)?;

    // Load keys back
    let loaded_private_hex = fs::read_to_string("private_key.hex")?;
    let loaded_private = PrivateKey::from_hex(&loaded_private_hex)?;

    let loaded_public_b64 = fs::read_to_string("public_key.b64")?;
    let loaded_public = PublicKey::from_base64(&loaded_public_b64)?;

    // Verify keys match
    assert_eq!(private_key.to_bytes(), loaded_private.to_bytes());
    assert_eq!(public_key.to_bytes(), loaded_public.to_bytes());

    println!("✅ Keys stored and loaded successfully");
    Ok(())
}'>
                                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                            <rect x="9" y="9" width="13" height="13" rx="2" ry="2"/>
                                            <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"/>
                                        </svg>
                                    </button>
                                </div>
                                <pre><code class="language-rust">use forge_ec::{PrivateKey, PublicKey, encoding::{hex, base64}};
use std::fs;

fn key_storage_example() -> Result<(), Box<dyn std::error::Error>> {
    // Generate a key pair
    let private_key = PrivateKey::new();
    let public_key = private_key.public_key();

    // Store private key securely (hex format)
    let private_hex = hex::encode(&private_key)?;
    fs::write("private_key.hex", private_hex)?;

    // Store public key (base64 format for sharing)
    let public_b64 = base64::encode(&public_key)?;
    fs::write("public_key.b64", public_b64)?;

    // Load keys back
    let loaded_private_hex = fs::read_to_string("private_key.hex")?;
    let loaded_private = PrivateKey::from_hex(&loaded_private_hex)?;

    let loaded_public_b64 = fs::read_to_string("public_key.b64")?;
    let loaded_public = PublicKey::from_base64(&loaded_public_b64)?;

    // Verify keys match
    assert_eq!(private_key.to_bytes(), loaded_private.to_bytes());
    assert_eq!(public_key.to_bytes(), loaded_public.to_bytes());

    println!("✅ Keys stored and loaded successfully");
    Ok(())
}</code></pre>
                            </div>
                        </section>

                        <section id="error-handling" class="docs-section">
                            <h2>Error Handling</h2>
                            <p>
                                Proper error handling for encoding and decoding operations.
                            </p>

                            <div class="code-block-container">
                                <div class="code-block-header">
                                    <span class="code-block-title">Error Handling Example</span>
                                    <button class="copy-btn" data-copy='use forge_ec::{PublicKey, encoding::{hex, EncodingError}};

fn error_handling_example() {
    // Handle invalid hex input
    match PublicKey::from_hex("invalid_hex_string") {
        Ok(key) => println!("Key loaded: {:?}", key),
        Err(EncodingError::InvalidHex(msg)) => {
            eprintln!("Invalid hex format: {}", msg);
        }
        Err(EncodingError::InvalidLength { expected, actual }) => {
            eprintln!("Invalid length: expected {}, got {}", expected, actual);
        }
        Err(EncodingError::InvalidPoint(msg)) => {
            eprintln!("Invalid point: {}", msg);
        }
        Err(e) => eprintln!("Other error: {}", e),
    }

    // Handle invalid base64 input
    match base64::decode("invalid_base64!@#") {
        Ok(data) => println!("Decoded: {} bytes", data.len()),
        Err(EncodingError::InvalidBase64(msg)) => {
            eprintln!("Invalid base64: {}", msg);
        }
        Err(e) => eprintln!("Error: {}", e),
    }
}'>
                                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                            <rect x="9" y="9" width="13" height="13" rx="2" ry="2"/>
                                            <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"/>
                                        </svg>
                                    </button>
                                </div>
                                <pre><code class="language-rust">use forge_ec::{PublicKey, encoding::{hex, EncodingError}};

fn error_handling_example() {
    // Handle invalid hex input
    match PublicKey::from_hex("invalid_hex_string") {
        Ok(key) => println!("Key loaded: {:?}", key),
        Err(EncodingError::InvalidHex(msg)) => {
            eprintln!("Invalid hex format: {}", msg);
        }
        Err(EncodingError::InvalidLength { expected, actual }) => {
            eprintln!("Invalid length: expected {}, got {}", expected, actual);
        }
        Err(EncodingError::InvalidPoint(msg)) => {
            eprintln!("Invalid point: {}", msg);
        }
        Err(e) => eprintln!("Other error: {}", e),
    }

    // Handle invalid base64 input
    match base64::decode("invalid_base64!@#") {
        Ok(data) => println!("Decoded: {} bytes", data.len()),
        Err(EncodingError::InvalidBase64(msg)) => {
            eprintln!("Invalid base64: {}", msg);
        }
        Err(e) => eprintln!("Error: {}", e),
    }
}</code></pre>
                            </div>

                            <div class="warning-box">
                                <div class="warning-icon">
                                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                        <path d="M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z"/>
                                        <line x1="12" y1="9" x2="12" y2="13"/>
                                        <line x1="12" y1="17" x2="12.01" y2="17"/>
                                    </svg>
                                </div>
                                <div class="warning-content">
                                    <strong>Security Considerations:</strong>
                                    <ul>
                                        <li>Always validate input data before decoding</li>
                                        <li>Use constant-time comparison for sensitive data</li>
                                        <li>Clear sensitive data from memory after use</li>
                                        <li>Be aware of timing attacks in encoding operations</li>
                                    </ul>
                                </div>
                            </div>
                        </section>
                    </article>
                </div>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="docs-footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <p>&copy; 2024 Forge EC. Built with ❤️ by <a href="https://github.com/tanm-sys" target="_blank">Tanmay Patil</a></p>
                </div>
                <div class="footer-section">
                    <div class="footer-links">
                        <a href="../../index.html#about">About</a>
                        <a href="../../index.html#community">Community</a>
                        <a href="../security/guidelines.html">Security</a>
                        <a href="https://github.com/tanm-sys/forge-ec" target="_blank">GitHub</a>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <!-- Firebase Configuration -->
    <script type="module">
        // Suppress browser extension errors that don't affect functionality
        const originalConsoleError = console.error;
        console.error = function(...args) {
            const message = args.join(' ');
            // Filter out known browser extension errors
            if (message.includes('message port closed') ||
                message.includes('Extension context invalidated') ||
                message.includes('runtime.lastError')) {
                return; // Suppress these harmless extension errors
            }
            originalConsoleError.apply(console, args);
        };

        try {
            // Firebase CDN imports with timeout protection
            const importTimeout = setTimeout(() => {
                console.warn('Firebase import timeout - continuing without Firebase');
                window.firebaseInitialized = false;
                window.dispatchEvent(new CustomEvent('firebaseReady'));
            }, 5000);

            Promise.all([
                import('https://www.gstatic.com/firebasejs/11.8.1/firebase-app.js'),
                import('https://www.gstatic.com/firebasejs/11.8.1/firebase-firestore.js'),
                import('https://www.gstatic.com/firebasejs/11.8.1/firebase-auth.js')
            ]).then(([appModule, firestoreModule, authModule]) => {
                clearTimeout(importTimeout);

                // Firebase configuration
                const firebaseConfig = {
                    apiKey: "AIzaSyDBG9YcnodA8Lhpwb3wOoyp93VcqXygcrQ",
                    authDomain: "forge-ec.firebaseapp.com",
                    databaseURL: "https://forge-ec-default-rtdb.firebaseio.com",
                    projectId: "forge-ec",
                    storageBucket: "forge-ec.firebasestorage.app",
                    messagingSenderId: "436060720516",
                    appId: "1:436060720516:web:4c4ac16371db82fcfd61d1",
                    measurementId: "G-1BVB7FLGRJ"
                };

                // Initialize Firebase
                const app = appModule.initializeApp(firebaseConfig);
                const db = firestoreModule.getFirestore(app);
                const auth = authModule.getAuth(app);

                // Make Firebase services globally available
                window.firebaseApp = app;
                window.firebaseDb = db;
                window.firebaseAuth = auth;
                window.firebaseInitialized = true;

                // Dispatch ready event
                window.dispatchEvent(new CustomEvent('firebaseReady'));
                console.log('✅ Firebase initialized successfully');
            }).catch(error => {
                clearTimeout(importTimeout);
                console.warn('Firebase initialization failed:', error);
                window.firebaseInitialized = false;
                window.dispatchEvent(new CustomEvent('firebaseReady'));
            });
        } catch (error) {
            console.warn('Firebase setup failed:', error);
            window.firebaseInitialized = false;
            window.dispatchEvent(new CustomEvent('firebaseReady'));
        }
    </script>

    <!-- Scripts -->
    <script src="../docs.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/components/prism-core.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/plugins/autoloader/prism-autoloader.min.js"></script>

    <!-- Unified Loading Screen Management -->
    <script>
        // Check if global LoadingScreenManager is available, otherwise use fallback
        function initializeLoadingScreenProtection() {
            // If docs.js LoadingScreenManager is available, it will handle everything
            if (window.LoadingScreenManager) {
                console.log('🔄 Using global LoadingScreenManager');
                return;
            }

            // Fallback protection if docs.js fails to load
            console.log('🔄 Using fallback loading screen protection');

            let fallbackHidden = false;
            function fallbackHide(reason) {
                if (fallbackHidden || window.loadingScreenHidden) return;

                const loadingScreen = document.getElementById('loading-screen');
                if (loadingScreen) {
                    console.log(`🔄 Fallback hiding loading screen: ${reason}`);
                    fallbackHidden = true;
                    window.loadingScreenHidden = true;

                    loadingScreen.style.opacity = '0';
                    loadingScreen.style.pointerEvents = 'none';
                    loadingScreen.style.transition = 'opacity 0.3s ease-out';

                    setTimeout(() => {
                        if (loadingScreen.parentNode) {
                            loadingScreen.style.display = 'none';
                            setTimeout(() => {
                                if (loadingScreen.parentNode) {
                                    loadingScreen.remove();
                                }
                            }, 100);
                        }
                    }, 300);
                }
            }

            // Fallback timeouts
            setTimeout(() => fallbackHide('Fallback timeout (3s)'), 3000);
            setTimeout(() => fallbackHide('Fallback timeout (6s)'), 6000);

            // Fallback event listeners
            window.addEventListener('load', () => setTimeout(() => fallbackHide('Fallback page load'), 500));
            document.addEventListener('DOMContentLoaded', () => setTimeout(() => fallbackHide('Fallback DOM loaded'), 1000));

            ['click', 'keydown', 'touchstart'].forEach(event => {
                document.addEventListener(event, () => fallbackHide('Fallback user interaction'), { once: true });
            });
        }

        // Initialize immediately
        initializeLoadingScreenProtection();

        // Also try after a short delay in case docs.js loads later
        setTimeout(initializeLoadingScreenProtection, 100);
    </script>
</body>
</html>

<svg viewBox="0 0 32 32" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="faviconGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#3b82f6"/>
      <stop offset="100%" style="stop-color:#8b5cf6"/>
    </linearGradient>
  </defs>
  
  <!-- Background circle -->
  <circle cx="16" cy="16" r="15" fill="url(#faviconGradient)"/>
  
  <!-- Elliptic curve -->
  <path d="M8,16 Q16,8 24,16 Q16,24 8,16" stroke="white" stroke-width="2" fill="none"/>
  
  <!-- Center point -->
  <circle cx="16" cy="16" r="2" fill="white"/>
  
  <!-- Additional curve points -->
  <circle cx="12" cy="12" r="1" fill="white" opacity="0.8"/>
  <circle cx="20" cy="12" r="1" fill="white" opacity="0.8"/>
  <circle cx="12" cy="20" r="1" fill="white" opacity="0.8"/>
  <circle cx="20" cy="20" r="1" fill="white" opacity="0.8"/>
</svg>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Vulnerability Disclosure - Forge EC Security</title>
    
    <!-- SEO Meta Tags -->
    <meta name="description" content="How to responsibly report security vulnerabilities in Forge EC. Security disclosure policy and contact information.">
    <meta name="keywords" content="forge ec, vulnerability disclosure, security, responsible disclosure, bug bounty">
    <meta name="author" content="Tanmay Patil">
    
    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="Vulnerability Disclosure - Forge EC Security">
    <meta property="og:description" content="Responsible security vulnerability reporting">
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://forge-ec.dev/docs/security/vulnerability-disclosure.html">
    
    <!-- Stylesheets -->
    <link rel="stylesheet" href="../../css/style.css">
    <link rel="stylesheet" href="../../css/animations.css">
    <link rel="stylesheet" href="../../css/components.css">
    <link rel="stylesheet" href="../docs.css">
    
    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="../../assets/favicon.svg">
    
    <!-- Syntax Highlighting -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/themes/prism-tomorrow.min.css">
</head>
<body>
    <!-- Loading Screen -->
    <div class="loading-screen" id="loading-screen">
        <div class="loading-content">
            <div class="loading-logo">
                <svg viewBox="0 0 40 40" xmlns="http://www.w3.org/2000/svg">
                    <path d="M8,20 Q20,8 32,20 Q20,32 8,20" stroke="currentColor" stroke-width="2" fill="none"/>
                    <circle cx="20" cy="20" r="2" fill="currentColor"/>
                </svg>
            </div>
            <div class="loading-text">Loading Vulnerability Disclosure Policy...</div>
            <div class="loading-progress">
                <div class="loading-bar" id="loading-bar"></div>
            </div>
        </div>
    </div>

    <!-- Navigation -->
    <nav class="navbar docs-navbar" id="navbar">
        <div class="nav-container">
            <div class="nav-brand">
                <a href="../../index.html" class="brand-link">
                    <svg class="brand-logo" viewBox="0 0 40 40" xmlns="http://www.w3.org/2000/svg">
                        <path d="M8,20 Q20,8 32,20 Q20,32 8,20" stroke="currentColor" stroke-width="2" fill="none"/>
                        <circle cx="20" cy="20" r="2" fill="currentColor"/>
                    </svg>
                    <span class="brand-text">Forge EC</span>
                </a>
            </div>

            <div class="nav-menu" id="nav-menu">
                <a href="../../index.html" class="nav-link">Home</a>
                <a href="../../index.html#features" class="nav-link">Features</a>
                <a href="../../index.html#about" class="nav-link">About</a>
                <a href="../index.html" class="nav-link active">Documentation</a>
                <a href="../../index.html#examples" class="nav-link">Examples</a>
                <a href="../../index.html#community" class="nav-link">Community</a>
                <a href="../../index.html#contact" class="nav-link">Contact</a>
            </div>

            <div class="nav-actions">
                <button class="theme-toggle" id="theme-toggle" aria-label="Toggle theme">
                    <svg class="theme-icon sun-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                        <circle cx="12" cy="12" r="5"/>
                        <path d="M12 1v2M12 21v2M4.22 4.22l1.42 1.42M18.36 18.36l1.42 1.42M1 12h2M21 12h2M4.22 19.78l1.42-1.42M18.36 5.64l1.42-1.42"/>
                    </svg>
                    <svg class="theme-icon moon-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                        <path d="M21 12.79A9 9 0 1 1 11.21 3 7 7 0 0 0 21 12.79z"/>
                    </svg>
                </button>
                <button class="auth-btn" id="auth-btn">
                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                        <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"/>
                        <circle cx="12" cy="7" r="4"/>
                    </svg>
                    <span>Sign In</span>
                </button>
                <button class="mobile-menu-toggle" id="mobile-menu-toggle" aria-label="Toggle mobile menu">
                    <span></span>
                    <span></span>
                    <span></span>
                </button>
            </div>
        </div>
    </nav>

    <!-- Documentation Content -->
    <main class="docs-main">
        <!-- Documentation Header -->
        <div class="docs-header">
            <div class="container">
                <div class="docs-header-content">
                    <div class="docs-meta">
                        <span class="docs-category">Security</span>
                        <span class="docs-level beginner">Beginner</span>
                        <span class="docs-time">5 min read</span>
                    </div>
                    <h1 class="docs-title">Vulnerability Disclosure</h1>
                    <p class="docs-subtitle">
                        How to responsibly report security vulnerabilities in Forge EC. 
                        We appreciate the security community's help in keeping our users safe.
                    </p>
                    <div class="docs-actions">
                        <button class="bookmark-btn" id="bookmark-btn" aria-label="Bookmark this page">
                            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                <path d="M19 21l-7-5-7 5V5a2 2 0 0 1 2-2h10a2 2 0 0 1 2 2z"/>
                            </svg>
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Documentation Content -->
        <div class="docs-content">
            <div class="container">
                <div class="docs-layout">
                    <!-- Table of Contents -->
                    <aside class="docs-sidebar">
                        <div class="toc-container glass-enhanced">
                            <h3 class="toc-title">Table of Contents</h3>
                            <nav class="toc" id="toc">
                                <ul class="toc-list">
                                    <li class="toc-item">
                                        <a href="#overview" class="toc-link">Overview</a>
                                    </li>
                                    <li class="toc-item">
                                        <a href="#scope" class="toc-link">Scope</a>
                                    </li>
                                    <li class="toc-item">
                                        <a href="#reporting" class="toc-link">Reporting Process</a>
                                    </li>
                                    <li class="toc-item">
                                        <a href="#response" class="toc-link">Response Timeline</a>
                                    </li>
                                    <li class="toc-item">
                                        <a href="#guidelines" class="toc-link">Guidelines</a>
                                    </li>
                                    <li class="toc-item">
                                        <a href="#recognition" class="toc-link">Recognition</a>
                                    </li>
                                    <li class="toc-item">
                                        <a href="#contact" class="toc-link">Contact Information</a>
                                    </li>
                                </ul>
                            </nav>
                        </div>
                    </aside>

                    <!-- Main Content -->
                    <article class="docs-article">
                        <section id="overview" class="docs-section">
                            <h2>Overview</h2>
                            <p>
                                Forge EC takes security seriously. We appreciate the security community's efforts 
                                to responsibly disclose vulnerabilities and help us maintain the highest security 
                                standards for our cryptographic library.
                            </p>

                            <div class="info-box">
                                <div class="info-icon">
                                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                        <circle cx="12" cy="12" r="10"/>
                                        <path d="M12 16v-4M12 8h.01"/>
                                    </svg>
                                </div>
                                <div class="info-content">
                                    <strong>Security First:</strong>
                                    <p>We are committed to addressing security vulnerabilities promptly and transparently. 
                                    Your responsible disclosure helps protect all Forge EC users.</p>
                                </div>
                            </div>
                        </section>

                        <section id="scope" class="docs-section">
                            <h2>Scope</h2>
                            <p>
                                This vulnerability disclosure policy covers security issues in the Forge EC library 
                                and its official documentation and examples.
                            </p>

                            <h3>In Scope</h3>
                            <ul class="docs-list">
                                <li><strong>Cryptographic vulnerabilities:</strong> Implementation flaws in cryptographic algorithms</li>
                                <li><strong>Side-channel attacks:</strong> Timing attacks, cache attacks, power analysis vulnerabilities</li>
                                <li><strong>Memory safety issues:</strong> Buffer overflows, use-after-free, memory leaks with security implications</li>
                                <li><strong>API security flaws:</strong> Insecure defaults, dangerous API designs</li>
                                <li><strong>Dependency vulnerabilities:</strong> Security issues in critical dependencies</li>
                                <li><strong>Documentation security issues:</strong> Misleading security guidance or insecure examples</li>
                            </ul>

                            <h3>Out of Scope</h3>
                            <ul class="docs-list">
                                <li>Issues in third-party applications using Forge EC</li>
                                <li>Theoretical attacks without practical exploitation</li>
                                <li>Issues requiring physical access to the target system</li>
                                <li>Social engineering attacks</li>
                                <li>Denial of service attacks without security implications</li>
                            </ul>
                        </section>

                        <section id="reporting" class="docs-section">
                            <h2>Reporting Process</h2>
                            <p>
                                Please follow these steps to report a security vulnerability:
                            </p>

                            <div class="warning-box">
                                <div class="warning-icon">
                                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                        <path d="M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z"/>
                                        <line x1="12" y1="9" x2="12" y2="13"/>
                                        <line x1="12" y1="17" x2="12.01" y2="17"/>
                                    </svg>
                                </div>
                                <div class="warning-content">
                                    <strong>Do NOT create public GitHub issues for security vulnerabilities!</strong>
                                    <p>Please use our private security reporting channels to avoid exposing users to risk.</p>
                                </div>
                            </div>

                            <h3>Step 1: Prepare Your Report</h3>
                            <p>Include the following information in your vulnerability report:</p>
                            <ul class="docs-list">
                                <li><strong>Vulnerability description:</strong> Clear explanation of the security issue</li>
                                <li><strong>Affected versions:</strong> Which versions of Forge EC are affected</li>
                                <li><strong>Impact assessment:</strong> Potential security impact and attack scenarios</li>
                                <li><strong>Proof of concept:</strong> Code or steps to reproduce the vulnerability</li>
                                <li><strong>Suggested fix:</strong> If you have ideas for remediation</li>
                                <li><strong>Your contact information:</strong> For follow-up questions</li>
                            </ul>

                            <h3>Step 2: Submit Your Report</h3>
                            <p>Send your vulnerability report to:</p>
                            <div class="code-block-container">
                                <div class="code-block-header">
                                    <span class="code-block-title">Security Contact</span>
                                    <button class="copy-btn" data-copy='Email: <EMAIL>
Subject: [SECURITY] Vulnerability Report - [Brief Description]

Alternative: GitHub Security Advisory
https://github.com/tanm-sys/forge-ec/security/advisories/new'>
                                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                            <rect x="9" y="9" width="13" height="13" rx="2" ry="2"/>
                                            <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"/>
                                        </svg>
                                    </button>
                                </div>
                                <pre><code>Email: <EMAIL>
Subject: [SECURITY] Vulnerability Report - [Brief Description]

Alternative: GitHub Security Advisory
https://github.com/tanm-sys/forge-ec/security/advisories/new</code></pre>
                            </div>
                        </section>

                        <section id="response" class="docs-section">
                            <h2>Response Timeline</h2>
                            <p>
                                We are committed to responding to security reports promptly:
                            </p>

                            <div class="timeline">
                                <div class="timeline-item">
                                    <div class="timeline-marker">1</div>
                                    <div class="timeline-content">
                                        <h4>Initial Response</h4>
                                        <p><strong>Within 24 hours:</strong> Acknowledgment of your report</p>
                                    </div>
                                </div>
                                <div class="timeline-item">
                                    <div class="timeline-marker">2</div>
                                    <div class="timeline-content">
                                        <h4>Assessment</h4>
                                        <p><strong>Within 72 hours:</strong> Initial assessment and severity classification</p>
                                    </div>
                                </div>
                                <div class="timeline-item">
                                    <div class="timeline-marker">3</div>
                                    <div class="timeline-content">
                                        <h4>Resolution</h4>
                                        <p><strong>Critical: 7 days, High: 30 days, Medium: 90 days</strong></p>
                                    </div>
                                </div>
                                <div class="timeline-item">
                                    <div class="timeline-marker">4</div>
                                    <div class="timeline-content">
                                        <h4>Disclosure</h4>
                                        <p><strong>After fix:</strong> Coordinated public disclosure</p>
                                    </div>
                                </div>
                            </div>
                        </section>

                        <section id="guidelines" class="docs-section">
                            <h2>Disclosure Guidelines</h2>
                            <p>
                                Please follow these guidelines when reporting security vulnerabilities:
                            </p>

                            <h3>Responsible Disclosure Principles</h3>
                            <ul class="docs-list">
                                <li><strong>Private reporting:</strong> Report vulnerabilities privately before public disclosure</li>
                                <li><strong>Reasonable time:</strong> Allow reasonable time for fixes before public disclosure</li>
                                <li><strong>No exploitation:</strong> Do not exploit vulnerabilities beyond proof-of-concept</li>
                                <li><strong>No data access:</strong> Do not access or modify user data</li>
                                <li><strong>Coordinated disclosure:</strong> Work with us on disclosure timing</li>
                            </ul>

                            <h3>What NOT to Do</h3>
                            <div class="warning-box">
                                <div class="warning-icon">
                                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                        <path d="M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z"/>
                                        <line x1="12" y1="9" x2="12" y2="13"/>
                                        <line x1="12" y1="17" x2="12.01" y2="17"/>
                                    </svg>
                                </div>
                                <div class="warning-content">
                                    <strong>Prohibited Activities:</strong>
                                    <ul>
                                        <li>Creating public GitHub issues for security vulnerabilities</li>
                                        <li>Posting vulnerabilities on social media or forums</li>
                                        <li>Attempting to access production systems or user data</li>
                                        <li>Performing denial of service attacks</li>
                                        <li>Social engineering attacks against team members</li>
                                    </ul>
                                </div>
                            </div>
                        </section>

                        <section id="recognition" class="docs-section">
                            <h2>Recognition Program</h2>
                            <p>
                                We appreciate security researchers who help improve Forge EC's security.
                            </p>

                            <h3>Hall of Fame</h3>
                            <p>
                                Researchers who responsibly disclose valid security vulnerabilities will be recognized in our:
                            </p>
                            <ul class="docs-list">
                                <li><strong>Security Hall of Fame:</strong> Listed on our website and documentation</li>
                                <li><strong>Release Notes:</strong> Credited in security update announcements</li>
                                <li><strong>CVE Credits:</strong> Named in CVE database entries (when applicable)</li>
                                <li><strong>Social Recognition:</strong> Acknowledged on our social media channels</li>
                            </ul>

                            <h3>Severity Classifications</h3>
                            <div class="info-box">
                                <div class="info-icon">
                                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                        <circle cx="12" cy="12" r="10"/>
                                        <path d="M12 16v-4M12 8h.01"/>
                                    </svg>
                                </div>
                                <div class="info-content">
                                    <strong>Vulnerability Severity Levels:</strong>
                                    <ul>
                                        <li><strong>Critical:</strong> Remote code execution, cryptographic breaks</li>
                                        <li><strong>High:</strong> Privilege escalation, significant data exposure</li>
                                        <li><strong>Medium:</strong> Information disclosure, denial of service</li>
                                        <li><strong>Low:</strong> Minor security improvements, edge cases</li>
                                    </ul>
                                </div>
                            </div>
                        </section>

                        <section id="contact" class="docs-section">
                            <h2>Contact Information</h2>
                            <p>
                                Multiple ways to securely report security vulnerabilities:
                            </p>

                            <h3>Primary Contact Methods</h3>
                            <div class="code-block-container">
                                <div class="code-block-header">
                                    <span class="code-block-title">Security Contact Details</span>
                                    <button class="copy-btn" data-copy='Primary Email: <EMAIL>
GitHub Security: https://github.com/tanm-sys/forge-ec/security/advisories/new
Maintainer: Tanmay Patil (tanm-sys)
Response Time: Within 24 hours

PGP Key Fingerprint: [Available on request]
Signal: [Available on request for critical issues]'>
                                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                            <rect x="9" y="9" width="13" height="13" rx="2" ry="2"/>
                                            <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"/>
                                        </svg>
                                    </button>
                                </div>
                                <pre><code>Primary Email: <EMAIL>
GitHub Security: https://github.com/tanm-sys/forge-ec/security/advisories/new
Maintainer: Tanmay Patil (tanm-sys)
Response Time: Within 24 hours

PGP Key Fingerprint: [Available on request]
Signal: [Available on request for critical issues]</code></pre>
                            </div>

                            <h3>Encrypted Communication</h3>
                            <p>
                                For highly sensitive vulnerabilities, we support encrypted communication:
                            </p>
                            <ul class="docs-list">
                                <li><strong>PGP/GPG:</strong> Request our public key via email</li>
                                <li><strong>Signal:</strong> Available for critical vulnerabilities</li>
                                <li><strong>GitHub Security Advisories:</strong> Built-in private reporting</li>
                            </ul>

                            <div class="info-box">
                                <div class="info-icon">
                                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                        <circle cx="12" cy="12" r="10"/>
                                        <path d="M12 16v-4M12 8h.01"/>
                                    </svg>
                                </div>
                                <div class="info-content">
                                    <strong>Thank You!</strong>
                                    <p>Your security research helps protect all Forge EC users. We're committed to working
                                    with the security community to maintain the highest standards of cryptographic security.</p>
                                </div>
                            </div>
                        </section>
                    </article>
                </div>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="docs-footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <p>&copy; 2024 Forge EC. Built with ❤️ by <a href="https://github.com/tanm-sys" target="_blank">Tanmay Patil</a></p>
                </div>
                <div class="footer-section">
                    <div class="footer-links">
                        <a href="../../index.html#about">About</a>
                        <a href="../../index.html#community">Community</a>
                        <a href="guidelines.html">Security</a>
                        <a href="https://github.com/tanm-sys/forge-ec" target="_blank">GitHub</a>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <!-- Firebase Configuration -->
    <script type="module">
        // Suppress browser extension errors that don't affect functionality
        const originalConsoleError = console.error;
        console.error = function(...args) {
            const message = args.join(' ');
            // Filter out known browser extension errors
            if (message.includes('message port closed') ||
                message.includes('Extension context invalidated') ||
                message.includes('runtime.lastError')) {
                return; // Suppress these harmless extension errors
            }
            originalConsoleError.apply(console, args);
        };

        try {
            // Firebase CDN imports with timeout protection
            const importTimeout = setTimeout(() => {
                console.warn('Firebase import timeout - continuing without Firebase');
                window.firebaseInitialized = false;
                window.dispatchEvent(new CustomEvent('firebaseReady'));
            }, 5000);

            Promise.all([
                import('https://www.gstatic.com/firebasejs/11.8.1/firebase-app.js'),
                import('https://www.gstatic.com/firebasejs/11.8.1/firebase-firestore.js'),
                import('https://www.gstatic.com/firebasejs/11.8.1/firebase-auth.js')
            ]).then(([appModule, firestoreModule, authModule]) => {
                clearTimeout(importTimeout);

                // Firebase configuration
                const firebaseConfig = {
                    apiKey: "AIzaSyDBG9YcnodA8Lhpwb3wOoyp93VcqXygcrQ",
                    authDomain: "forge-ec.firebaseapp.com",
                    databaseURL: "https://forge-ec-default-rtdb.firebaseio.com",
                    projectId: "forge-ec",
                    storageBucket: "forge-ec.firebasestorage.app",
                    messagingSenderId: "436060720516",
                    appId: "1:436060720516:web:4c4ac16371db82fcfd61d1",
                    measurementId: "G-1BVB7FLGRJ"
                };

                // Initialize Firebase
                const app = appModule.initializeApp(firebaseConfig);
                const db = firestoreModule.getFirestore(app);
                const auth = authModule.getAuth(app);

                // Make Firebase services globally available
                window.firebaseApp = app;
                window.firebaseDb = db;
                window.firebaseAuth = auth;
                window.firebaseInitialized = true;

                // Dispatch ready event
                window.dispatchEvent(new CustomEvent('firebaseReady'));
                console.log('✅ Firebase initialized successfully');
            }).catch(error => {
                clearTimeout(importTimeout);
                console.warn('Firebase initialization failed:', error);
                window.firebaseInitialized = false;
                window.dispatchEvent(new CustomEvent('firebaseReady'));
            });
        } catch (error) {
            console.warn('Firebase setup failed:', error);
            window.firebaseInitialized = false;
            window.dispatchEvent(new CustomEvent('firebaseReady'));
        }
    </script>

    <!-- Scripts -->
    <script src="../docs.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/components/prism-core.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/plugins/autoloader/prism-autoloader.min.js"></script>

    <!-- Unified Loading Screen Management -->
    <script>
        // Check if global LoadingScreenManager is available, otherwise use fallback
        function initializeLoadingScreenProtection() {
            // If docs.js LoadingScreenManager is available, it will handle everything
            if (window.LoadingScreenManager) {
                console.log('🔄 Using global LoadingScreenManager');
                return;
            }

            // Fallback protection if docs.js fails to load
            console.log('🔄 Using fallback loading screen protection');

            let fallbackHidden = false;
            function fallbackHide(reason) {
                if (fallbackHidden || window.loadingScreenHidden) return;

                const loadingScreen = document.getElementById('loading-screen');
                if (loadingScreen) {
                    console.log(`🔄 Fallback hiding loading screen: ${reason}`);
                    fallbackHidden = true;
                    window.loadingScreenHidden = true;

                    loadingScreen.style.opacity = '0';
                    loadingScreen.style.pointerEvents = 'none';
                    loadingScreen.style.transition = 'opacity 0.3s ease-out';

                    setTimeout(() => {
                        if (loadingScreen.parentNode) {
                            loadingScreen.style.display = 'none';
                            setTimeout(() => {
                                if (loadingScreen.parentNode) {
                                    loadingScreen.remove();
                                }
                            }, 100);
                        }
                    }, 300);
                }
            }

            // Fallback timeouts
            setTimeout(() => fallbackHide('Fallback timeout (3s)'), 3000);
            setTimeout(() => fallbackHide('Fallback timeout (6s)'), 6000);

            // Fallback event listeners
            window.addEventListener('load', () => setTimeout(() => fallbackHide('Fallback page load'), 500));
            document.addEventListener('DOMContentLoaded', () => setTimeout(() => fallbackHide('Fallback DOM loaded'), 1000));

            ['click', 'keydown', 'touchstart'].forEach(event => {
                document.addEventListener(event, () => fallbackHide('Fallback user interaction'), { once: true });
            });
        }

        // Initialize immediately
        initializeLoadingScreenProtection();

        // Also try after a short delay in case docs.js loads later
        setTimeout(initializeLoadingScreenProtection, 100);
    </script>
</body>
</html>

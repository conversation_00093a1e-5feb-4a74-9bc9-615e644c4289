<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Signatures Module - Forge EC API Reference</title>
    
    <!-- SEO Meta Tags -->
    <meta name="description" content="Complete API reference for Forge EC Signatures module. ECDSA, EdDSA, and Schnorr signature implementations with code examples.">
    <meta name="keywords" content="forge ec, signatures, ECDSA, EdDSA, Schnorr, API, rust, cryptography">
    <meta name="author" content="Tanmay Patil">
    
    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="Signatures Module - Forge EC API Reference">
    <meta property="og:description" content="Complete API reference for digital signature implementations">
    <meta property="og:type" content="article">
    <meta property="og:url" content="https://forge-ec.dev/docs/api/signatures.html">
    
    <!-- Stylesheets -->
    <link rel="stylesheet" href="../../css/style.css">
    <link rel="stylesheet" href="../../css/animations.css">
    <link rel="stylesheet" href="../../css/components.css">
    <link rel="stylesheet" href="../docs.css">
    
    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="../../assets/favicon.svg">
    
    <!-- Syntax Highlighting -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/themes/prism-tomorrow.min.css">
</head>
<body>
    <!-- Loading Screen -->
    <div class="loading-screen" id="loading-screen">
        <div class="loading-content">
            <div class="loading-logo">
                <svg viewBox="0 0 40 40" xmlns="http://www.w3.org/2000/svg">
                    <path d="M8,20 Q20,8 32,20 Q20,32 8,20" stroke="currentColor" stroke-width="2" fill="none"/>
                    <circle cx="20" cy="20" r="2" fill="currentColor"/>
                </svg>
            </div>
            <div class="loading-text">Loading API Reference...</div>
            <div class="loading-progress">
                <div class="loading-bar" id="loading-bar"></div>
            </div>
        </div>
    </div>

    <!-- Navigation -->
    <nav class="navbar docs-navbar" id="navbar">
        <div class="nav-container">
            <div class="nav-brand">
                <a href="../../index.html" class="brand-link">
                    <svg class="brand-logo" viewBox="0 0 40 40" xmlns="http://www.w3.org/2000/svg">
                        <path d="M8,20 Q20,8 32,20 Q20,32 8,20" stroke="currentColor" stroke-width="2" fill="none"/>
                        <circle cx="20" cy="20" r="2" fill="currentColor"/>
                    </svg>
                    <span class="brand-text">Forge EC</span>
                </a>
            </div>

            <div class="nav-menu" id="nav-menu">
                <a href="../../index.html" class="nav-link">Home</a>
                <a href="../../index.html#features" class="nav-link">Features</a>
                <a href="../../index.html#about" class="nav-link">About</a>
                <a href="../../index.html#docs" class="nav-link active">Documentation</a>
                <a href="../../index.html#examples" class="nav-link">Examples</a>
                <a href="../../index.html#community" class="nav-link">Community</a>
                <a href="../../index.html#contact" class="nav-link">Contact</a>
            </div>

            <div class="nav-actions">
                <button class="theme-toggle" id="theme-toggle" aria-label="Toggle theme">
                    <svg class="theme-icon sun-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                        <circle cx="12" cy="12" r="5"/>
                        <path d="M12 1v2M12 21v2M4.22 4.22l1.42 1.42M18.36 18.36l1.42 1.42M1 12h2M21 12h2M4.22 19.78l1.42-1.42M18.36 5.64l1.42-1.42"/>
                    </svg>
                    <svg class="theme-icon moon-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                        <path d="M21 12.79A9 9 0 1 1 11.21 3 7 7 0 0 0 21 12.79z"/>
                    </svg>
                </button>
                <button class="auth-btn" id="auth-btn">
                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                        <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"/>
                        <circle cx="12" cy="7" r="4"/>
                    </svg>
                    <span>Sign In</span>
                </button>
                <button class="mobile-menu-toggle" id="mobile-menu-toggle" aria-label="Toggle mobile menu">
                    <span></span>
                    <span></span>
                    <span></span>
                </button>
            </div>
        </div>
    </nav>

    <!-- Documentation Content -->
    <main class="docs-main">
        <!-- Breadcrumb Navigation -->
        <div class="breadcrumb-container">
            <div class="container">
                <nav class="breadcrumb" aria-label="Breadcrumb">
                    <ol class="breadcrumb-list">
                        <li class="breadcrumb-item">
                            <a href="../../index.html#docs" class="breadcrumb-link">Documentation</a>
                        </li>
                        <li class="breadcrumb-item">
                            <a href="../api.html" class="breadcrumb-link">API Reference</a>
                        </li>
                        <li class="breadcrumb-item active" aria-current="page">Signatures Module</li>
                    </ol>
                </nav>
            </div>
        </div>

        <!-- Documentation Header -->
        <div class="docs-header">
            <div class="container">
                <div class="docs-header-content">
                    <div class="docs-meta">
                        <span class="docs-category">API Reference</span>
                        <span class="docs-level intermediate">Intermediate</span>
                        <span class="docs-time">15 min read</span>
                    </div>
                    <h1 class="docs-title">Signatures Module</h1>
                    <p class="docs-subtitle">
                        Complete API reference for digital signature implementations including ECDSA, EdDSA, 
                        and Schnorr signatures with practical examples and security considerations.
                    </p>
                    <div class="docs-actions">
                        <button class="bookmark-btn" id="bookmark-btn" aria-label="Bookmark this page">
                            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                <path d="M19 21l-7-5-7 5V5a2 2 0 0 1 2-2h10a2 2 0 0 1 2 2z"/>
                            </svg>
                        </button>
                        <div class="reading-progress">
                            <div class="progress-bar" id="reading-progress"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Documentation Content -->
        <div class="docs-content">
            <div class="container">
                <div class="docs-layout">
                    <!-- Table of Contents -->
                    <aside class="docs-sidebar">
                        <div class="toc-container glass-enhanced">
                            <h3 class="toc-title">Table of Contents</h3>
                            <nav class="toc" id="toc">
                                <ul class="toc-list">
                                    <li class="toc-item">
                                        <a href="#overview" class="toc-link">Overview</a>
                                    </li>
                                    <li class="toc-item">
                                        <a href="#ecdsa" class="toc-link">ECDSA Signatures</a>
                                    </li>
                                    <li class="toc-item">
                                        <a href="#eddsa" class="toc-link">EdDSA Signatures</a>
                                    </li>
                                    <li class="toc-item">
                                        <a href="#schnorr" class="toc-link">Schnorr Signatures</a>
                                    </li>
                                    <li class="toc-item">
                                        <a href="#key-management" class="toc-link">Key Management</a>
                                    </li>
                                    <li class="toc-item">
                                        <a href="#error-handling" class="toc-link">Error Handling</a>
                                    </li>
                                    <li class="toc-item">
                                        <a href="#security" class="toc-link">Security Notes</a>
                                    </li>
                                </ul>
                            </nav>
                        </div>
                    </aside>

                    <!-- Main Content -->
                    <article class="docs-article">
                        <section id="overview" class="docs-section">
                            <h2>Overview</h2>
                            <p>
                                The Forge EC Signatures module provides secure, high-performance implementations of 
                                industry-standard digital signature algorithms. All implementations are designed with 
                                constant-time operations to prevent timing attacks.
                            </p>

                            <h3>Supported Algorithms</h3>
                            <ul class="docs-list">
                                <li><strong>ECDSA:</strong> Elliptic Curve Digital Signature Algorithm (FIPS 186-4)</li>
                                <li><strong>EdDSA:</strong> Edwards-curve Digital Signature Algorithm (RFC 8032)</li>
                                <li><strong>Schnorr:</strong> Schnorr signatures (BIP 340)</li>
                            </ul>

                            <h3>Key Features</h3>
                            <ul class="docs-list">
                                <li>Constant-time implementations</li>
                                <li>Memory-safe Rust code</li>
                                <li>Support for multiple curves</li>
                                <li>Comprehensive error handling</li>
                                <li>Zero-copy serialization</li>
                                <li>WebAssembly compatibility</li>
                            </ul>

                            <div class="info-box">
                                <div class="info-icon">
                                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                        <circle cx="12" cy="12" r="10"/>
                                        <path d="M12 16v-4M12 8h.01"/>
                                    </svg>
                                </div>
                                <div class="info-content">
                                    <strong>Import Statement:</strong> All signature types are available through the main signatures module.
                                    <div class="code-block" style="margin-top: 8px;">
                                        <pre><code class="language-rust">use forge_ec::signatures::*;</code></pre>
                                    </div>
                                </div>
                            </div>
                        </section>

                        <section id="ecdsa" class="docs-section">
                            <h2>ECDSA Signatures</h2>
                            <p>
                                Elliptic Curve Digital Signature Algorithm (ECDSA) is widely used in blockchain 
                                applications and TLS. Forge EC provides a secure, RFC-compliant implementation.
                            </p>

                            <h3>Basic Usage</h3>
                            <div class="code-block">
                                <div class="code-header">
                                    <span class="code-title">ECDSA Example</span>
                                    <button class="copy-btn" data-copy='use forge_ec::signatures::ecdsa::*;
use forge_ec::curves::secp256k1::Secp256k1;

// Generate a new ECDSA keypair
let private_key = PrivateKey::<Secp256k1>::new();
let public_key = private_key.public_key();

// Message to sign
let message = b"Hello, ECDSA!";

// Create signature
let signature = private_key.sign(message)?;

// Verify signature
let is_valid = public_key.verify(message, &signature)?;
assert!(is_valid);

println!("ECDSA signature verified successfully!");'>
                                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                            <rect x="9" y="9" width="13" height="13" rx="2" ry="2"/>
                                            <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"/>
                                        </svg>
                                    </button>
                                </div>
                                <pre><code class="language-rust">use forge_ec::signatures::ecdsa::*;
use forge_ec::curves::secp256k1::Secp256k1;

// Generate a new ECDSA keypair
let private_key = PrivateKey::<Secp256k1>::new();
let public_key = private_key.public_key();

// Message to sign
let message = b"Hello, ECDSA!";

// Create signature
let signature = private_key.sign(message)?;

// Verify signature
let is_valid = public_key.verify(message, &signature)?;
assert!(is_valid);

println!("ECDSA signature verified successfully!");</code></pre>
                            </div>

                            <h3>Supported Curves</h3>
                            <ul class="docs-list">
                                <li><strong>secp256k1:</strong> Bitcoin and Ethereum standard</li>
                                <li><strong>secp256r1 (P-256):</strong> NIST standard curve</li>
                                <li><strong>secp384r1 (P-384):</strong> Higher security NIST curve</li>
                                <li><strong>secp521r1 (P-521):</strong> Highest security NIST curve</li>
                            </ul>

                            <h3>Advanced ECDSA Features</h3>
                            <div class="code-block">
                                <div class="code-header">
                                    <span class="code-title">Advanced ECDSA Usage</span>
                                    <button class="copy-btn" data-copy='use forge_ec::signatures::ecdsa::*;
use forge_ec::curves::secp256k1::Secp256k1;
use forge_ec::hashing::sha256;

// Custom hash function
let private_key = PrivateKey::<Secp256k1>::new();
let message = b"Custom hashed message";
let hash = sha256(message);

// Sign pre-hashed message
let signature = private_key.sign_prehashed(&hash)?;

// Verify with custom hash
let public_key = private_key.public_key();
let is_valid = public_key.verify_prehashed(&hash, &signature)?;

// Signature serialization
let signature_bytes = signature.to_bytes();
let signature_der = signature.to_der();
let signature_hex = signature.to_hex();

// Signature deserialization
let recovered_sig = Signature::from_bytes(&signature_bytes)?;
let recovered_der = Signature::from_der(&signature_der)?;
let recovered_hex = Signature::from_hex(&signature_hex)?;'>
                                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                            <rect x="9" y="9" width="13" height="13" rx="2" ry="2"/>
                                            <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"/>
                                        </svg>
                                    </button>
                                </div>
                                <pre><code class="language-rust">use forge_ec::signatures::ecdsa::*;
use forge_ec::curves::secp256k1::Secp256k1;
use forge_ec::hashing::sha256;

// Custom hash function
let private_key = PrivateKey::<Secp256k1>::new();
let message = b"Custom hashed message";
let hash = sha256(message);

// Sign pre-hashed message
let signature = private_key.sign_prehashed(&hash)?;

// Verify with custom hash
let public_key = private_key.public_key();
let is_valid = public_key.verify_prehashed(&hash, &signature)?;

// Signature serialization
let signature_bytes = signature.to_bytes();
let signature_der = signature.to_der();
let signature_hex = signature.to_hex();

// Signature deserialization
let recovered_sig = Signature::from_bytes(&signature_bytes)?;
let recovered_der = Signature::from_der(&signature_der)?;
let recovered_hex = Signature::from_hex(&signature_hex)?;</code></pre>
                            </div>
                        </section>

                        <section id="eddsa" class="docs-section">
                            <h2>EdDSA Signatures</h2>
                            <p>
                                Edwards-curve Digital Signature Algorithm (EdDSA) provides fast signature generation 
                                and verification with built-in protection against many side-channel attacks.
                            </p>

                            <h3>Ed25519 Implementation</h3>
                            <div class="code-block">
                                <div class="code-header">
                                    <span class="code-title">Ed25519 Example</span>
                                    <button class="copy-btn" data-copy='use forge_ec::signatures::eddsa::*;
use forge_ec::curves::ed25519::Ed25519;

// Generate Ed25519 keypair
let private_key = PrivateKey::<Ed25519>::new();
let public_key = private_key.public_key();

// Message to sign
let message = b"Hello, Ed25519!";

// Create EdDSA signature
let signature = private_key.sign(message)?;

// Verify signature
let is_valid = public_key.verify(message, &signature)?;
assert!(is_valid);

// Ed25519 signatures are deterministic
let signature2 = private_key.sign(message)?;
assert_eq!(signature, signature2);

println!("Ed25519 signature verified successfully!");'>
                                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                            <rect x="9" y="9" width="13" height="13" rx="2" ry="2"/>
                                            <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"/>
                                        </svg>
                                    </button>
                                </div>
                                <pre><code class="language-rust">use forge_ec::signatures::eddsa::*;
use forge_ec::curves::ed25519::Ed25519;

// Generate Ed25519 keypair
let private_key = PrivateKey::<Ed25519>::new();
let public_key = private_key.public_key();

// Message to sign
let message = b"Hello, Ed25519!";

// Create EdDSA signature
let signature = private_key.sign(message)?;

// Verify signature
let is_valid = public_key.verify(message, &signature)?;
assert!(is_valid);

// Ed25519 signatures are deterministic
let signature2 = private_key.sign(message)?;
assert_eq!(signature, signature2);

println!("Ed25519 signature verified successfully!");</code></pre>
                            </div>

                            <div class="success-box">
                                <div class="success-icon">
                                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                        <path d="M9 12l2 2 4-4M21 12c0 4.97-4.03 9-9 9s-9-4.03-9-9 4.03-9 9-9 9 4.03 9 9z"/>
                                    </svg>
                                </div>
                                <div class="success-content">
                                    <strong>Performance Advantage:</strong> Ed25519 signatures are typically 2-3x faster 
                                    than ECDSA while providing equivalent security levels.
                                </div>
                            </div>
                        </section>

                        <section id="schnorr" class="docs-section">
                            <h2>Schnorr Signatures</h2>
                            <p>
                                Schnorr signatures provide excellent security properties and are used in Bitcoin's Taproot upgrade.
                                They offer key aggregation and batch verification capabilities.
                            </p>

                            <h3>Basic Schnorr Implementation</h3>
                            <div class="code-block">
                                <div class="code-header">
                                    <span class="code-title">Schnorr Example</span>
                                    <button class="copy-btn" data-copy='use forge_ec::signatures::schnorr::*;
use forge_ec::curves::secp256k1::Secp256k1;

// Generate Schnorr keypair
let private_key = PrivateKey::<Secp256k1>::new();
let public_key = private_key.public_key();

// Message to sign
let message = b"Hello, Schnorr!";

// Create Schnorr signature
let signature = private_key.sign(message)?;

// Verify signature
let is_valid = public_key.verify(message, &signature)?;
assert!(is_valid);

println!("Schnorr signature verified successfully!");'>
                                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                            <rect x="9" y="9" width="13" height="13" rx="2" ry="2"/>
                                            <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"/>
                                        </svg>
                                    </button>
                                </div>
                                <pre><code class="language-rust">use forge_ec::signatures::schnorr::*;
use forge_ec::curves::secp256k1::Secp256k1;

// Generate Schnorr keypair
let private_key = PrivateKey::<Secp256k1>::new();
let public_key = private_key.public_key();

// Message to sign
let message = b"Hello, Schnorr!";

// Create Schnorr signature
let signature = private_key.sign(message)?;

// Verify signature
let is_valid = public_key.verify(message, &signature)?;
assert!(is_valid);

println!("Schnorr signature verified successfully!");</code></pre>
                            </div>
                        </section>

                        <section id="key-management" class="docs-section">
                            <h2>Key Management</h2>
                            <p>
                                Proper key management is essential for cryptographic security. Forge EC provides
                                secure key generation, serialization, and storage utilities.
                            </p>

                            <h3>Key Generation</h3>
                            <div class="code-block">
                                <div class="code-header">
                                    <span class="code-title">Secure Key Generation</span>
                                    <button class="copy-btn" data-copy='use forge_ec::signatures::ecdsa::PrivateKey;
use forge_ec::curves::secp256k1::Secp256k1;
use forge_ec::rng::SecureRng;

// Use cryptographically secure RNG
let mut rng = SecureRng::new();
let private_key = PrivateKey::<Secp256k1>::generate(&mut rng);

// Derive public key
let public_key = private_key.public_key();

// Secure serialization
let private_bytes = private_key.to_secure_bytes();
let public_bytes = public_key.to_bytes();'>
                                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                            <rect x="9" y="9" width="13" height="13" rx="2" ry="2"/>
                                            <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"/>
                                        </svg>
                                    </button>
                                </div>
                                <pre><code class="language-rust">use forge_ec::signatures::ecdsa::PrivateKey;
use forge_ec::curves::secp256k1::Secp256k1;
use forge_ec::rng::SecureRng;

// Use cryptographically secure RNG
let mut rng = SecureRng::new();
let private_key = PrivateKey::<Secp256k1>::generate(&mut rng);

// Derive public key
let public_key = private_key.public_key();

// Secure serialization
let private_bytes = private_key.to_secure_bytes();
let public_bytes = public_key.to_bytes();</code></pre>
                            </div>
                        </section>

                        <section id="error-handling" class="docs-section">
                            <h2>Error Handling</h2>
                            <p>
                                Forge EC provides comprehensive error handling for all signature operations.
                                Always handle errors appropriately in production code.
                            </p>

                            <div class="code-block">
                                <div class="code-header">
                                    <span class="code-title">Error Handling Example</span>
                                    <button class="copy-btn" data-copy='use forge_ec::signatures::ecdsa::*;
use forge_ec::errors::*;

fn safe_signature_verification(
    public_key: &PublicKey,
    message: &[u8],
    signature: &Signature
) -> Result<bool, SignatureError> {
    match public_key.verify(message, signature) {
        Ok(is_valid) => {
            if is_valid {
                println!("✅ Signature verification successful");
            } else {
                println!("❌ Signature verification failed");
            }
            Ok(is_valid)
        }
        Err(e) => {
            eprintln!("Error during verification: {}", e);
            Err(e)
        }
    }
}'>
                                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                            <rect x="9" y="9" width="13" height="13" rx="2" ry="2"/>
                                            <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"/>
                                        </svg>
                                    </button>
                                </div>
                                <pre><code class="language-rust">use forge_ec::signatures::ecdsa::*;
use forge_ec::errors::*;

fn safe_signature_verification(
    public_key: &PublicKey,
    message: &[u8],
    signature: &Signature
) -> Result<bool, SignatureError> {
    match public_key.verify(message, signature) {
        Ok(is_valid) => {
            if is_valid {
                println!("✅ Signature verification successful");
            } else {
                println!("❌ Signature verification failed");
            }
            Ok(is_valid)
        }
        Err(e) => {
            eprintln!("Error during verification: {}", e);
            Err(e)
        }
    }
}</code></pre>
                            </div>
                        </section>

                        <section id="security" class="docs-section">
                            <h2>Security Notes</h2>
                            <p>
                                Important security considerations when using digital signatures:
                            </p>

                            <div class="warning-box">
                                <div class="warning-icon">
                                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                        <path d="M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z"/>
                                        <line x1="12" y1="9" x2="12" y2="13"/>
                                        <line x1="12" y1="17" x2="12.01" y2="17"/>
                                    </svg>
                                </div>
                                <div class="warning-content">
                                    <strong>Security Guidelines:</strong>
                                    <ul>
                                        <li>Never reuse nonces in signature generation</li>
                                        <li>Always use cryptographically secure random number generators</li>
                                        <li>Protect private keys with appropriate access controls</li>
                                        <li>Validate all inputs before processing</li>
                                        <li>Use constant-time operations to prevent timing attacks</li>
                                    </ul>
                                </div>
                            </div>
                        </section>
                    </article>
                </div>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="docs-footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <p>&copy; 2024 Forge EC. Built with ❤️ by <a href="https://github.com/tanm-sys" target="_blank">Tanmay Patil</a></p>
                </div>
                <div class="footer-section">
                    <div class="footer-links">
                        <a href="../../index.html#about">About</a>
                        <a href="../../index.html#community">Community</a>
                        <a href="../security/vulnerability-disclosure.html">Security</a>
                        <a href="https://github.com/tanm-sys/forge-ec" target="_blank">GitHub</a>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <!-- Firebase Configuration -->
    <script type="module">
        // Suppress browser extension errors that don't affect functionality
        const originalConsoleError = console.error;
        console.error = function(...args) {
            const message = args.join(' ');
            // Filter out known browser extension errors
            if (message.includes('message port closed') ||
                message.includes('Extension context invalidated') ||
                message.includes('runtime.lastError')) {
                return; // Suppress these harmless extension errors
            }
            originalConsoleError.apply(console, args);
        };

        try {
            // Firebase CDN imports with timeout protection
            const importTimeout = setTimeout(() => {
                console.warn('Firebase import timeout - continuing without Firebase');
                window.firebaseInitialized = false;
                window.dispatchEvent(new CustomEvent('firebaseReady'));
            }, 5000);

            Promise.all([
                import('https://www.gstatic.com/firebasejs/11.8.1/firebase-app.js'),
                import('https://www.gstatic.com/firebasejs/11.8.1/firebase-firestore.js'),
                import('https://www.gstatic.com/firebasejs/11.8.1/firebase-auth.js')
            ]).then(([appModule, firestoreModule, authModule]) => {
                clearTimeout(importTimeout);

                // Firebase configuration
                const firebaseConfig = {
                    apiKey: "AIzaSyDBG9YcnodA8Lhpwb3wOoyp93VcqXygcrQ",
                    authDomain: "forge-ec.firebaseapp.com",
                    databaseURL: "https://forge-ec-default-rtdb.firebaseio.com",
                    projectId: "forge-ec",
                    storageBucket: "forge-ec.firebasestorage.app",
                    messagingSenderId: "436060720516",
                    appId: "1:436060720516:web:4c4ac16371db82fcfd61d1",
                    measurementId: "G-1BVB7FLGRJ"
                };

                // Initialize Firebase
                const app = appModule.initializeApp(firebaseConfig);
                const db = firestoreModule.getFirestore(app);
                const auth = authModule.getAuth(app);

                // Make Firebase services globally available
                window.firebaseApp = app;
                window.firebaseDb = db;
                window.firebaseAuth = auth;
                window.firebaseInitialized = true;

                // Dispatch ready event
                window.dispatchEvent(new CustomEvent('firebaseReady'));
                console.log('✅ Firebase initialized successfully');
            }).catch(error => {
                clearTimeout(importTimeout);
                console.warn('Firebase initialization failed:', error);
                window.firebaseInitialized = false;
                window.dispatchEvent(new CustomEvent('firebaseReady'));
            });
        } catch (error) {
            console.warn('Firebase setup failed:', error);
            window.firebaseInitialized = false;
            window.dispatchEvent(new CustomEvent('firebaseReady'));
        }
    </script>

    <!-- Scripts -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/components/prism-core.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/plugins/autoloader/prism-autoloader.min.js"></script>
    <script src="../docs.js"></script>

    <!-- Unified Loading Screen Management -->
    <script>
        // Check if global LoadingScreenManager is available, otherwise use fallback
        function initializeLoadingScreenProtection() {
            // If docs.js LoadingScreenManager is available, it will handle everything
            if (window.LoadingScreenManager) {
                console.log('🔄 Using global LoadingScreenManager');
                return;
            }

            // Fallback protection if docs.js fails to load
            console.log('🔄 Using fallback loading screen protection');

            let fallbackHidden = false;
            function fallbackHide(reason) {
                if (fallbackHidden || window.loadingScreenHidden) return;

                const loadingScreen = document.getElementById('loading-screen');
                if (loadingScreen) {
                    console.log(`🔄 Fallback hiding loading screen: ${reason}`);
                    fallbackHidden = true;
                    window.loadingScreenHidden = true;

                    loadingScreen.style.opacity = '0';
                    loadingScreen.style.pointerEvents = 'none';
                    loadingScreen.style.transition = 'opacity 0.3s ease-out';

                    setTimeout(() => {
                        if (loadingScreen.parentNode) {
                            loadingScreen.style.display = 'none';
                            setTimeout(() => {
                                if (loadingScreen.parentNode) {
                                    loadingScreen.remove();
                                }
                            }, 100);
                        }
                    }, 300);
                }
            }

            // Fallback timeouts
            setTimeout(() => fallbackHide('Fallback timeout (3s)'), 3000);
            setTimeout(() => fallbackHide('Fallback timeout (6s)'), 6000);

            // Fallback event listeners
            window.addEventListener('load', () => setTimeout(() => fallbackHide('Fallback page load'), 500));
            document.addEventListener('DOMContentLoaded', () => setTimeout(() => fallbackHide('Fallback DOM loaded'), 1000));

            ['click', 'keydown', 'touchstart'].forEach(event => {
                document.addEventListener(event, () => fallbackHide('Fallback user interaction'), { once: true });
            });
        }

        // Initialize immediately
        initializeLoadingScreenProtection();

        // Also try after a short delay in case docs.js loads later
        setTimeout(initializeLoadingScreenProtection, 100);
    </script>
</body>
</html>

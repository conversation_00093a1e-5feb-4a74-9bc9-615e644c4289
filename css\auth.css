/* ===== AUTHENTICATION STYLES ===== */

/* Modal Overlay */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(10px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10000;
  opacity: 0;
  /* animation: fadeIn 0.3s ease-out forwards; */ /* Now controlled by JS */
}

/*
@keyframes fadeIn {
  to {
    opacity: 1;
  }
}
*/

/* Modal Content */
.modal-content {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 20px;
  padding: 0;
  max-width: 450px;
  width: 90%;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 
    0 20px 40px rgba(0, 0, 0, 0.3),
    0 0 0 1px rgba(255, 255, 255, 0.1);
  transform: scale(0.9) translateY(20px);
  animation: modalSlideIn 0.3s ease-out forwards;
  transition: background-color var(--transition-normal), border-color var(--transition-normal), box-shadow var(--transition-normal);
}

@keyframes modalSlideIn {
  to {
    transform: scale(1) translateY(0);
  }
}

/* Modal Header */
.modal-header {
  padding: 24px 24px 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.modal-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--color-text-primary);
  margin: 0;
  transition: color var(--transition-normal);
}

.modal-close {
  background: none;
  border: none;
  font-size: 24px;
  color: var(--color-text-secondary);
  cursor: pointer;
  padding: 8px;
  border-radius: 8px;
  transition: all 0.2s ease;
}

.modal-close:hover {
  background: rgba(255, 255, 255, 0.1);
  color: var(--color-text-primary);
}

/* Modal Body */
.modal-body {
  padding: 24px;
}

/* Auth Tabs */
.auth-tabs {
  display: flex;
  margin-bottom: 24px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  padding: 4px;
  transition: background-color var(--transition-normal);
}

.auth-tab {
  flex: 1;
  padding: 12px 16px;
  background: none;
  border: none;
  border-radius: 8px;
  color: var(--color-text-secondary);
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.auth-tab.active {
  background: rgba(255, 255, 255, 0.1);
  color: var(--color-text-primary);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* Auth Forms */
.auth-form {
  display: none;
}

.auth-form.active {
  display: block;
}

/* Divider */
.divider {
  position: relative;
  text-align: center;
  margin: 16px 0 24px 0; /* Adjusted top margin */
  color: var(--color-text-secondary);
  transition: color var(--transition-normal);
}

.divider::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 1px;
  background: rgba(255, 255, 255, 0.1);
}

.divider span {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  padding: 0 16px;
  position: relative;
  transition: background-color var(--transition-normal);
}

/* Form Groups */
.form-group {
  margin-bottom: 16px;
}

.form-group input,
.form-group select,
.form-group textarea {
  width: 100%;
  padding: 12px 16px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  color: var(--color-text-primary);
  font-size: 14px;
  transition: all 0.2s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: var(--color-primary);
  background: rgba(255, 255, 255, 0.08);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-group input::placeholder {
  color: var(--color-text-secondary);
}

/* Submit Button */
.auth-submit-btn {
  width: 100%;
  padding: 12px 16px;
  background: linear-gradient(135deg, var(--color-primary), var(--color-secondary));
  border: none;
  border-radius: 12px;
  color: white;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  margin-top: 8px;
}

.auth-submit-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.3);
}

/* Auth Links */
.auth-links {
  text-align: center;
  margin-top: 16px;
}

.auth-links a {
  color: var(--color-primary);
  text-decoration: none;
  font-size: 14px;
  transition: color 0.2s ease;
}

.auth-links a:hover {
  color: var(--color-secondary);
}

/* Auth Feedback */
.auth-feedback {
  position: fixed;
  top: 20px;
  right: 20px;
  padding: 12px 20px;
  border-radius: 12px;
  color: white;
  font-weight: 500;
  z-index: 10001;
  transform: translateX(100%);
  animation: slideInRight 0.3s ease-out forwards;
  max-width: 300px;
}

@keyframes slideInRight {
  to {
    transform: translateX(0);
  }
}

.auth-feedback.success {
  background: linear-gradient(135deg, #10b981, #059669);
}

.auth-feedback.error {
  background: linear-gradient(135deg, #ef4444, #dc2626);
}

.auth-feedback.info {
  background: linear-gradient(135deg, #3b82f6, #2563eb);
}

/* User Menu Trigger */
.user-menu-trigger {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.user-menu-trigger:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.2);
}

.user-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  object-fit: cover;
}

.user-name {
  color: var(--color-text-primary);
  font-weight: 500;
  font-size: 14px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .modal-content {
    width: 95%;
    margin: 20px;
  }
  
  .modal-header,
  .modal-body {
    padding: 20px;
  }
  
  .auth-feedback {
    right: 10px;
    left: 10px;
    max-width: none;
  }
}

/* Dark Theme Adjustments */
[data-theme="dark"] .modal-content {
  background: rgba(0, 0, 0, 0.8);
  border-color: rgba(255, 255, 255, 0.1);
}

[data-theme="dark"] .form-group input,
[data-theme="dark"] .form-group select,
[data-theme="dark"] .form-group textarea {
  background: rgba(255, 255, 255, 0.03);
  border-color: rgba(255, 255, 255, 0.08);
}

/* Removed [data-theme="dark"] .social-btn as .social-btn is removed */

[data-theme="dark"] .auth-tab {
  background: rgba(255, 255, 255, 0.03);
}

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Security Guidelines - Forge EC Documentation</title>
    
    <!-- SEO Meta Tags -->
    <meta name="description" content="Essential security practices and guidelines for using Forge EC cryptography library safely in production applications.">
    <meta name="keywords" content="forge ec, security, cryptography, best practices, guidelines, rust">
    <meta name="author" content="Tanmay Patil">
    
    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="Security Guidelines - Forge EC Documentation">
    <meta property="og:description" content="Essential security practices for cryptographic implementations">
    <meta property="og:type" content="article">
    <meta property="og:url" content="https://forge-ec.dev/docs/security/guidelines.html">
    
    <!-- Stylesheets -->
    <link rel="stylesheet" href="../../css/style.css">
    <link rel="stylesheet" href="../../css/animations.css">
    <link rel="stylesheet" href="../../css/components.css">
    <link rel="stylesheet" href="../docs.css">
    
    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="../../assets/favicon.svg">
    
    <!-- Syntax Highlighting -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/themes/prism-tomorrow.min.css">
</head>
<body>
    <!-- Loading Screen -->
    <div class="loading-screen" id="loading-screen">
        <div class="loading-content">
            <div class="loading-logo">
                <svg viewBox="0 0 40 40" xmlns="http://www.w3.org/2000/svg">
                    <path d="M8,20 Q20,8 32,20 Q20,32 8,20" stroke="currentColor" stroke-width="2" fill="none"/>
                    <circle cx="20" cy="20" r="2" fill="currentColor"/>
                </svg>
            </div>
            <div class="loading-text">Loading Security Guidelines...</div>
            <div class="loading-progress">
                <div class="loading-bar" id="loading-bar"></div>
            </div>
        </div>
    </div>

    <!-- Navigation -->
    <nav class="navbar docs-navbar" id="navbar">
        <div class="nav-container">
            <div class="nav-brand">
                <a href="../../index.html" class="brand-link">
                    <svg class="brand-logo" viewBox="0 0 40 40" xmlns="http://www.w3.org/2000/svg">
                        <path d="M8,20 Q20,8 32,20 Q20,32 8,20" stroke="currentColor" stroke-width="2" fill="none"/>
                        <circle cx="20" cy="20" r="2" fill="currentColor"/>
                    </svg>
                    <span class="brand-text">Forge EC</span>
                </a>
            </div>

            <div class="nav-menu" id="nav-menu">
                <a href="../../index.html" class="nav-link">Home</a>
                <a href="../../index.html#features" class="nav-link">Features</a>
                <a href="../../index.html#about" class="nav-link">About</a>
                <a href="../../index.html#docs" class="nav-link active">Documentation</a>
                <a href="../../index.html#examples" class="nav-link">Examples</a>
                <a href="../../index.html#community" class="nav-link">Community</a>
                <a href="../../index.html#contact" class="nav-link">Contact</a>
            </div>

            <div class="nav-actions">
                <button class="theme-toggle" id="theme-toggle" aria-label="Toggle theme">
                    <svg class="theme-icon sun-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                        <circle cx="12" cy="12" r="5"/>
                        <path d="M12 1v2M12 21v2M4.22 4.22l1.42 1.42M18.36 18.36l1.42 1.42M1 12h2M21 12h2M4.22 19.78l1.42-1.42M18.36 5.64l1.42-1.42"/>
                    </svg>
                    <svg class="theme-icon moon-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                        <path d="M21 12.79A9 9 0 1 1 11.21 3 7 7 0 0 0 21 12.79z"/>
                    </svg>
                </button>
                <button class="auth-btn" id="auth-btn">
                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                        <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"/>
                        <circle cx="12" cy="7" r="4"/>
                    </svg>
                    <span>Sign In</span>
                </button>
                <button class="mobile-menu-toggle" id="mobile-menu-toggle" aria-label="Toggle mobile menu">
                    <span></span>
                    <span></span>
                    <span></span>
                </button>
            </div>
        </div>
    </nav>

    <!-- Documentation Content -->
    <main class="docs-main">
        <!-- Breadcrumb Navigation -->
        <div class="breadcrumb-container">
            <div class="container">
                <nav class="breadcrumb" aria-label="Breadcrumb">
                    <ol class="breadcrumb-list">
                        <li class="breadcrumb-item">
                            <a href="../../index.html#docs" class="breadcrumb-link">Documentation</a>
                        </li>
                        <li class="breadcrumb-item">
                            <a href="../security.html" class="breadcrumb-link">Security</a>
                        </li>
                        <li class="breadcrumb-item active" aria-current="page">Security Guidelines</li>
                    </ol>
                </nav>
            </div>
        </div>

        <!-- Documentation Header -->
        <div class="docs-header">
            <div class="container">
                <div class="docs-header-content">
                    <div class="docs-meta">
                        <span class="docs-category">Security</span>
                        <span class="docs-level advanced">Advanced</span>
                        <span class="docs-time">20 min read</span>
                    </div>
                    <h1 class="docs-title">Security Guidelines</h1>
                    <p class="docs-subtitle">
                        Essential security practices and common pitfalls to avoid when implementing 
                        cryptographic systems with Forge EC. Learn how to protect against various attack vectors.
                    </p>
                    <div class="docs-actions">
                        <button class="bookmark-btn" id="bookmark-btn" aria-label="Bookmark this page">
                            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                <path d="M19 21l-7-5-7 5V5a2 2 0 0 1 2-2h10a2 2 0 0 1 2 2z"/>
                            </svg>
                        </button>
                        <div class="reading-progress">
                            <div class="progress-bar" id="reading-progress"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Documentation Content -->
        <div class="docs-content">
            <div class="container">
                <div class="docs-layout">
                    <!-- Table of Contents -->
                    <aside class="docs-sidebar">
                        <div class="toc-container glass-enhanced">
                            <h3 class="toc-title">Table of Contents</h3>
                            <nav class="toc" id="toc">
                                <ul class="toc-list">
                                    <li class="toc-item">
                                        <a href="#overview" class="toc-link">Security Overview</a>
                                    </li>
                                    <li class="toc-item">
                                        <a href="#key-management" class="toc-link">Key Management</a>
                                    </li>
                                    <li class="toc-item">
                                        <a href="#random-generation" class="toc-link">Random Number Generation</a>
                                    </li>
                                    <li class="toc-item">
                                        <a href="#timing-attacks" class="toc-link">Timing Attack Prevention</a>
                                    </li>
                                    <li class="toc-item">
                                        <a href="#side-channels" class="toc-link">Side-Channel Protection</a>
                                    </li>
                                    <li class="toc-item">
                                        <a href="#common-pitfalls" class="toc-link">Common Pitfalls</a>
                                    </li>
                                    <li class="toc-item">
                                        <a href="#best-practices" class="toc-link">Best Practices</a>
                                    </li>
                                </ul>
                            </nav>
                        </div>
                    </aside>

                    <!-- Main Content -->
                    <article class="docs-article">
                        <section id="overview" class="docs-section">
                            <h2>Security Overview</h2>
                            <p>
                                Cryptographic security extends far beyond the mathematical correctness of algorithms. 
                                This guide covers essential security practices for implementing robust cryptographic 
                                systems using Forge EC.
                            </p>

                            <div class="error-box">
                                <div class="error-icon">
                                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                        <path d="M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z"/>
                                        <line x1="12" y1="9" x2="12" y2="13"/>
                                        <line x1="12" y1="17" x2="12.01" y2="17"/>
                                    </svg>
                                </div>
                                <div class="error-content">
                                    <strong>Critical Security Warning:</strong> Improper use of cryptographic primitives 
                                    can lead to complete compromise of your system's security. Always follow these guidelines 
                                    and consider professional security review for production systems.
                                </div>
                            </div>

                            <h3>Security Principles</h3>
                            <ul class="docs-list">
                                <li><strong>Defense in Depth:</strong> Multiple layers of security protection</li>
                                <li><strong>Least Privilege:</strong> Minimal access rights for components</li>
                                <li><strong>Fail Securely:</strong> Secure defaults and safe failure modes</li>
                                <li><strong>Complete Mediation:</strong> All access attempts must be checked</li>
                                <li><strong>Open Design:</strong> Security through design, not obscurity</li>
                            </ul>
                        </section>

                        <section id="key-management" class="docs-section">
                            <h2>Key Management</h2>
                            <p>
                                Proper key management is crucial for maintaining cryptographic security. 
                                Keys must be generated, stored, used, and destroyed securely.
                            </p>

                            <h3>Secure Key Generation</h3>
                            <div class="code-block">
                                <div class="code-header">
                                    <span class="code-title">Secure Key Generation</span>
                                    <button class="copy-btn" data-copy='use forge_ec::signatures::ecdsa::PrivateKey;
use forge_ec::curves::secp256k1::Secp256k1;
use forge_ec::rng::SecureRng;

// ✅ CORRECT: Use cryptographically secure RNG
let mut rng = SecureRng::new();
let private_key = PrivateKey::<Secp256k1>::generate(&mut rng);

// ❌ WRONG: Never use predictable sources
// let weak_key = PrivateKey::from_bytes(&[1, 2, 3, ...]);

// ✅ CORRECT: Generate multiple keys independently
let key1 = PrivateKey::<Secp256k1>::generate(&mut rng);
let key2 = PrivateKey::<Secp256k1>::generate(&mut rng);

// Verify keys are different
assert_ne!(key1.to_bytes(), key2.to_bytes());'>
                                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                            <rect x="9" y="9" width="13" height="13" rx="2" ry="2"/>
                                            <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"/>
                                        </svg>
                                    </button>
                                </div>
                                <pre><code class="language-rust">use forge_ec::signatures::ecdsa::PrivateKey;
use forge_ec::curves::secp256k1::Secp256k1;
use forge_ec::rng::SecureRng;

// ✅ CORRECT: Use cryptographically secure RNG
let mut rng = SecureRng::new();
let private_key = PrivateKey::<Secp256k1>::generate(&mut rng);

// ❌ WRONG: Never use predictable sources
// let weak_key = PrivateKey::from_bytes(&[1, 2, 3, ...]);

// ✅ CORRECT: Generate multiple keys independently
let key1 = PrivateKey::<Secp256k1>::generate(&mut rng);
let key2 = PrivateKey::<Secp256k1>::generate(&mut rng);

// Verify keys are different
assert_ne!(key1.to_bytes(), key2.to_bytes());</code></pre>
                            </div>

                            <h3>Secure Key Storage</h3>
                            <div class="warning-box">
                                <div class="warning-icon">
                                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                        <path d="M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z"/>
                                        <line x1="12" y1="9" x2="12" y2="13"/>
                                        <line x1="12" y1="17" x2="12.01" y2="17"/>
                                    </svg>
                                </div>
                                <div class="warning-content">
                                    <strong>Key Storage Rules:</strong>
                                    <ul>
                                        <li>Never store private keys in plain text</li>
                                        <li>Use hardware security modules (HSMs) when possible</li>
                                        <li>Encrypt keys at rest with strong passwords</li>
                                        <li>Implement secure key derivation functions</li>
                                        <li>Use secure memory allocation for key material</li>
                                    </ul>
                                </div>
                            </div>

                            <div class="code-block">
                                <div class="code-header">
                                    <span class="code-title">Secure Key Handling</span>
                                    <button class="copy-btn" data-copy='use forge_ec::signatures::ecdsa::PrivateKey;
use forge_ec::secure_memory::SecureBytes;
use zeroize::Zeroize;

// ✅ CORRECT: Use secure memory for sensitive data
let mut key_material = SecureBytes::new(32);
// ... populate key_material securely ...

let private_key = PrivateKey::from_secure_bytes(&key_material)?;

// ✅ CORRECT: Explicitly clear sensitive data
key_material.zeroize();

// ✅ CORRECT: Use RAII for automatic cleanup
{
    let private_key = PrivateKey::generate(&mut rng);
    // Use private_key...
    // Automatically zeroized when dropped
}

// ❌ WRONG: Leaving sensitive data in memory
// let key_hex = private_key.to_hex(); // Leaves copy in memory
// println!("Key: {}", key_hex); // Logs sensitive data'>
                                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                            <rect x="9" y="9" width="13" height="13" rx="2" ry="2"/>
                                            <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"/>
                                        </svg>
                                    </button>
                                </div>
                                <pre><code class="language-rust">use forge_ec::signatures::ecdsa::PrivateKey;
use forge_ec::secure_memory::SecureBytes;
use zeroize::Zeroize;

// ✅ CORRECT: Use secure memory for sensitive data
let mut key_material = SecureBytes::new(32);
// ... populate key_material securely ...

let private_key = PrivateKey::from_secure_bytes(&key_material)?;

// ✅ CORRECT: Explicitly clear sensitive data
key_material.zeroize();

// ✅ CORRECT: Use RAII for automatic cleanup
{
    let private_key = PrivateKey::generate(&mut rng);
    // Use private_key...
    // Automatically zeroized when dropped
}

// ❌ WRONG: Leaving sensitive data in memory
// let key_hex = private_key.to_hex(); // Leaves copy in memory
// println!("Key: {}", key_hex); // Logs sensitive data</code></pre>
                            </div>
                        </section>

                        <section id="random-generation" class="docs-section">
                            <h2>Random Number Generation</h2>
                            <p>
                                Cryptographic security depends heavily on high-quality randomness. Poor random 
                                number generation is one of the most common causes of cryptographic failures.
                            </p>

                            <h3>Entropy Sources</h3>
                            <div class="code-block">
                                <div class="code-header">
                                    <span class="code-title">Proper RNG Usage</span>
                                    <button class="copy-btn" data-copy='use forge_ec::rng::{SecureRng, EntropySource};

// ✅ CORRECT: Use system entropy sources
let mut rng = SecureRng::from_entropy();

// ✅ CORRECT: Seed with additional entropy if available
let mut rng = SecureRng::new();
rng.add_entropy(&additional_entropy_bytes);

// ✅ CORRECT: Check entropy quality
if rng.entropy_estimate() < 256 {
    return Err("Insufficient entropy");
}

// ❌ WRONG: Using predictable seeds
// let mut rng = SecureRng::from_seed([0u8; 32]);

// ❌ WRONG: Using standard library RNG for crypto
// use std::collections::hash_map::DefaultHasher;
// let weak_rng = DefaultHasher::new();'>
                                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                            <rect x="9" y="9" width="13" height="13" rx="2" ry="2"/>
                                            <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"/>
                                        </svg>
                                    </button>
                                </div>
                                <pre><code class="language-rust">use forge_ec::rng::{SecureRng, EntropySource};

// ✅ CORRECT: Use system entropy sources
let mut rng = SecureRng::from_entropy();

// ✅ CORRECT: Seed with additional entropy if available
let mut rng = SecureRng::new();
rng.add_entropy(&additional_entropy_bytes);

// ✅ CORRECT: Check entropy quality
if rng.entropy_estimate() < 256 {
    return Err("Insufficient entropy");
}

// ❌ WRONG: Using predictable seeds
// let mut rng = SecureRng::from_seed([0u8; 32]);

// ❌ WRONG: Using standard library RNG for crypto
// use std::collections::hash_map::DefaultHasher;
// let weak_rng = DefaultHasher::new();</code></pre>
                            </div>

                            <div class="info-box">
                                <div class="info-icon">
                                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                        <circle cx="12" cy="12" r="10"/>
                                        <path d="M12 16v-4M12 8h.01"/>
                                    </svg>
                                </div>
                                <div class="info-content">
                                    <strong>Entropy Guidelines:</strong>
                                    <ul>
                                        <li>Always use cryptographically secure random number generators</li>
                                        <li>Ensure sufficient entropy before generating keys</li>
                                        <li>Consider hardware random number generators for high-security applications</li>
                                        <li>Test randomness quality in your specific environment</li>
                                    </ul>
                                </div>
                            </div>
                        </section>

                        <section id="timing-attacks" class="docs-section">
                            <h2>Timing Attack Prevention</h2>
                            <p>
                                Timing attacks exploit variations in execution time to extract sensitive information. 
                                Forge EC implements constant-time operations to prevent these attacks.
                            </p>

                            <h3>Constant-Time Operations</h3>
                            <div class="code-block">
                                <div class="code-header">
                                    <span class="code-title">Timing-Safe Comparisons</span>
                                    <button class="copy-btn" data-copy='use forge_ec::constant_time::ConstantTimeEq;

// ✅ CORRECT: Use constant-time comparison
fn verify_signature_safe(
    signature1: &[u8], 
    signature2: &[u8]
) -> bool {
    signature1.ct_eq(signature2).into()
}

// ❌ WRONG: Variable-time comparison
fn verify_signature_unsafe(
    signature1: &[u8], 
    signature2: &[u8]
) -> bool {
    signature1 == signature2 // Leaks timing information!
}

// ✅ CORRECT: All Forge EC operations are constant-time
let is_valid = public_key.verify(message, &signature)?;

// The verification time is independent of:
// - The message content
// - The signature values  
// - Whether the signature is valid or invalid'>
                                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                            <rect x="9" y="9" width="13" height="13" rx="2" ry="2"/>
                                            <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"/>
                                        </svg>
                                    </button>
                                </div>
                                <pre><code class="language-rust">use forge_ec::constant_time::ConstantTimeEq;

// ✅ CORRECT: Use constant-time comparison
fn verify_signature_safe(
    signature1: &[u8], 
    signature2: &[u8]
) -> bool {
    signature1.ct_eq(signature2).into()
}

// ❌ WRONG: Variable-time comparison
fn verify_signature_unsafe(
    signature1: &[u8], 
    signature2: &[u8]
) -> bool {
    signature1 == signature2 // Leaks timing information!
}

// ✅ CORRECT: All Forge EC operations are constant-time
let is_valid = public_key.verify(message, &signature)?;

// The verification time is independent of:
// - The message content
// - The signature values  
// - Whether the signature is valid or invalid</code></pre>
                            </div>
                        </section>

                        <section id="side-channels" class="docs-section">
                            <h2>Side-Channel Protection</h2>
                            <p>
                                Side-channel attacks exploit information leaked through physical implementation
                                characteristics. Forge EC includes protections against common side-channel attacks.
                            </p>

                            <h3>Power Analysis Protection</h3>
                            <div class="info-box">
                                <div class="info-icon">
                                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                        <circle cx="12" cy="12" r="10"/>
                                        <path d="M12 16v-4M12 8h.01"/>
                                    </svg>
                                </div>
                                <div class="info-content">
                                    <strong>Built-in Protections:</strong>
                                    <ul>
                                        <li>Constant-time scalar multiplication</li>
                                        <li>Uniform memory access patterns</li>
                                        <li>Blinded operations for sensitive computations</li>
                                        <li>Secure memory clearing after use</li>
                                    </ul>
                                </div>
                            </div>
                        </section>

                        <section id="common-pitfalls" class="docs-section">
                            <h2>Common Pitfalls</h2>
                            <p>
                                Learn from common mistakes that can compromise cryptographic security:
                            </p>

                            <h3>Nonce Reuse</h3>
                            <div class="error-box">
                                <div class="error-icon">
                                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                        <circle cx="12" cy="12" r="10"/>
                                        <line x1="15" y1="9" x2="9" y2="15"/>
                                        <line x1="9" y1="9" x2="15" y2="15"/>
                                    </svg>
                                </div>
                                <div class="error-content">
                                    <strong>Critical Error:</strong> Reusing nonces in ECDSA signatures can lead to
                                    private key recovery. Forge EC automatically generates unique nonces for each signature.
                                </div>
                            </div>

                            <h3>Weak Random Number Generation</h3>
                            <div class="warning-box">
                                <div class="warning-icon">
                                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                        <path d="M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z"/>
                                        <line x1="12" y1="9" x2="12" y2="13"/>
                                        <line x1="12" y1="17" x2="12.01" y2="17"/>
                                    </svg>
                                </div>
                                <div class="warning-content">
                                    <strong>Common Mistake:</strong> Using predictable or low-entropy random sources
                                    for key generation. Always use cryptographically secure random number generators.
                                </div>
                            </div>

                            <h3>Improper Key Storage</h3>
                            <div class="error-box">
                                <div class="error-icon">
                                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                        <circle cx="12" cy="12" r="10"/>
                                        <line x1="15" y1="9" x2="9" y2="15"/>
                                        <line x1="9" y1="9" x2="15" y2="15"/>
                                    </svg>
                                </div>
                                <div class="error-content">
                                    <strong>Security Risk:</strong> Storing private keys in plain text, logs, or
                                    unencrypted databases. Use secure key management systems and encryption at rest.
                                </div>
                            </div>
                        </section>

                        <section id="best-practices" class="docs-section">
                            <h2>Best Practices</h2>
                            <p>
                                Follow these best practices for secure cryptographic implementations:
                            </p>

                            <h3>Development Guidelines</h3>
                            <ul class="docs-list">
                                <li><strong>Code Reviews:</strong> Have cryptographic code reviewed by security experts</li>
                                <li><strong>Testing:</strong> Implement comprehensive unit and integration tests</li>
                                <li><strong>Auditing:</strong> Regular security audits of cryptographic implementations</li>
                                <li><strong>Updates:</strong> Keep cryptographic libraries up to date</li>
                                <li><strong>Documentation:</strong> Document security assumptions and threat models</li>
                            </ul>

                            <h3>Operational Security</h3>
                            <ul class="docs-list">
                                <li><strong>Key Rotation:</strong> Implement regular key rotation policies</li>
                                <li><strong>Access Control:</strong> Limit access to cryptographic keys and operations</li>
                                <li><strong>Monitoring:</strong> Monitor for unusual cryptographic operations</li>
                                <li><strong>Incident Response:</strong> Have procedures for cryptographic compromises</li>
                                <li><strong>Backup & Recovery:</strong> Secure backup and recovery procedures for keys</li>
                            </ul>

                            <h3>Compliance Considerations</h3>
                            <div class="info-box">
                                <div class="info-icon">
                                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                        <circle cx="12" cy="12" r="10"/>
                                        <path d="M12 16v-4M12 8h.01"/>
                                    </svg>
                                </div>
                                <div class="info-content">
                                    <strong>Standards Compliance:</strong>
                                    <ul>
                                        <li>FIPS 140-2 for cryptographic modules</li>
                                        <li>Common Criteria for security evaluations</li>
                                        <li>NIST guidelines for cryptographic algorithms</li>
                                        <li>Industry-specific regulations (PCI DSS, HIPAA, etc.)</li>
                                    </ul>
                                </div>
                            </div>
                        </section>
                    </article>
                </div>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="docs-footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <p>&copy; 2024 Forge EC. Built with ❤️ by <a href="https://github.com/tanm-sys" target="_blank">Tanmay Patil</a></p>
                </div>
                <div class="footer-section">
                    <div class="footer-links">
                        <a href="../../index.html#about">About</a>
                        <a href="../../index.html#community">Community</a>
                        <a href="vulnerability-disclosure.html">Security</a>
                        <a href="https://github.com/tanm-sys/forge-ec" target="_blank">GitHub</a>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <!-- Firebase Configuration -->
    <script type="module">
        // Suppress browser extension errors that don't affect functionality
        const originalConsoleError = console.error;
        console.error = function(...args) {
            const message = args.join(' ');
            // Filter out known browser extension errors
            if (message.includes('message port closed') ||
                message.includes('Extension context invalidated') ||
                message.includes('runtime.lastError')) {
                return; // Suppress these harmless extension errors
            }
            originalConsoleError.apply(console, args);
        };

        try {
            // Firebase CDN imports with timeout protection
            const importTimeout = setTimeout(() => {
                console.warn('Firebase import timeout - continuing without Firebase');
                window.firebaseInitialized = false;
                window.dispatchEvent(new CustomEvent('firebaseReady'));
            }, 5000);

            Promise.all([
                import('https://www.gstatic.com/firebasejs/11.8.1/firebase-app.js'),
                import('https://www.gstatic.com/firebasejs/11.8.1/firebase-firestore.js'),
                import('https://www.gstatic.com/firebasejs/11.8.1/firebase-auth.js')
            ]).then(([appModule, firestoreModule, authModule]) => {
                clearTimeout(importTimeout);

                // Firebase configuration
                const firebaseConfig = {
                    apiKey: "AIzaSyDBG9YcnodA8Lhpwb3wOoyp93VcqXygcrQ",
                    authDomain: "forge-ec.firebaseapp.com",
                    databaseURL: "https://forge-ec-default-rtdb.firebaseio.com",
                    projectId: "forge-ec",
                    storageBucket: "forge-ec.firebasestorage.app",
                    messagingSenderId: "436060720516",
                    appId: "1:436060720516:web:4c4ac16371db82fcfd61d1",
                    measurementId: "G-1BVB7FLGRJ"
                };

                // Initialize Firebase
                const app = appModule.initializeApp(firebaseConfig);
                const db = firestoreModule.getFirestore(app);
                const auth = authModule.getAuth(app);

                // Make Firebase services globally available
                window.firebaseApp = app;
                window.firebaseDb = db;
                window.firebaseAuth = auth;
                window.firebaseInitialized = true;

                // Dispatch ready event
                window.dispatchEvent(new CustomEvent('firebaseReady'));
                console.log('✅ Firebase initialized successfully');
            }).catch(error => {
                clearTimeout(importTimeout);
                console.warn('Firebase initialization failed:', error);
                window.firebaseInitialized = false;
                window.dispatchEvent(new CustomEvent('firebaseReady'));
            });
        } catch (error) {
            console.warn('Firebase setup failed:', error);
            window.firebaseInitialized = false;
            window.dispatchEvent(new CustomEvent('firebaseReady'));
        }
    </script>

    <!-- Scripts -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/components/prism-core.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/plugins/autoloader/prism-autoloader.min.js"></script>
    <script src="../docs.js"></script>

    <!-- Unified Loading Screen Management -->
    <script>
        // Check if global LoadingScreenManager is available, otherwise use fallback
        function initializeLoadingScreenProtection() {
            // If docs.js LoadingScreenManager is available, it will handle everything
            if (window.LoadingScreenManager) {
                console.log('🔄 Using global LoadingScreenManager');
                return;
            }

            // Fallback protection if docs.js fails to load
            console.log('🔄 Using fallback loading screen protection');

            let fallbackHidden = false;
            function fallbackHide(reason) {
                if (fallbackHidden || window.loadingScreenHidden) return;

                const loadingScreen = document.getElementById('loading-screen');
                if (loadingScreen) {
                    console.log(`🔄 Fallback hiding loading screen: ${reason}`);
                    fallbackHidden = true;
                    window.loadingScreenHidden = true;

                    loadingScreen.style.opacity = '0';
                    loadingScreen.style.pointerEvents = 'none';
                    loadingScreen.style.transition = 'opacity 0.3s ease-out';

                    setTimeout(() => {
                        if (loadingScreen.parentNode) {
                            loadingScreen.style.display = 'none';
                            setTimeout(() => {
                                if (loadingScreen.parentNode) {
                                    loadingScreen.remove();
                                }
                            }, 100);
                        }
                    }, 300);
                }
            }

            // Fallback timeouts
            setTimeout(() => fallbackHide('Fallback timeout (3s)'), 3000);
            setTimeout(() => fallbackHide('Fallback timeout (6s)'), 6000);

            // Fallback event listeners
            window.addEventListener('load', () => setTimeout(() => fallbackHide('Fallback page load'), 500));
            document.addEventListener('DOMContentLoaded', () => setTimeout(() => fallbackHide('Fallback DOM loaded'), 1000));

            ['click', 'keydown', 'touchstart'].forEach(event => {
                document.addEventListener(event, () => fallbackHide('Fallback user interaction'), { once: true });
            });
        }

        // Initialize immediately
        initializeLoadingScreenProtection();

        // Also try after a short delay in case docs.js loads later
        setTimeout(initializeLoadingScreenProtection, 100);
    </script>
</body>
</html>
